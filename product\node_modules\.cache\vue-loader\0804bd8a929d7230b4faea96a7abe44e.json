{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue?vue&type=style&index=0&id=0df95efd&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue", "mtime": 1758774945802}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5wYXJ0eS1kaXN0cmlidXRpb24tY2hhcnQgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogMTAwJTsKfQoKLy8g6aW85Zu+KOWklumdoueahOWuueWZqCkKLmNvbnRhaW5lciB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwp9CgovLyDppbzlm77nmoTlpKflsI8KLmNoYXJ0c0dsIHsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7Cn0K"}, {"version": 3, "sources": ["PieChart3D.vue"], "names": [], "mappings": ";AAulBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "PieChart3D.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"party-distribution-chart\">\n    <!-- 3D饼图容器 -->\n    <div class=\"container\">\n      <div class=\"chartsGl\" :id=\"id\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'PartyDistributionChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      option: {},\n      isAnimating: false,\n      animationTimer: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.animationTimer) {\n      clearTimeout(this.animationTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 初始化构建3D饼图\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n\n      this.chart = echarts.init(chartContainer)\n\n      // 转换数据格式\n      const optionData = this.convertChartData()\n\n      // 传入数据生成 option ; getPie3D(数据，透明的空心占比（调节中间空心范围的0就是普通饼1就很镂空）)\n      this.option = this.getPie3D(optionData, 0.85)\n\n      // 将配置项设置进去\n      this.chart.setOption(this.option)\n\n      // 鼠标移动上去特效效果\n      this.bindListen(this.chart)\n\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    // 转换数据格式\n    convertChartData () {\n      if (!this.chartData || this.chartData.length === 0) {\n        return [\n          {\n            name: '中国共产党',\n            value: 32,\n            itemStyle: {\n              color: 'rgba(255, 107, 107, 0.8)'\n            }\n          },\n          {\n            name: '民革',\n            value: 15,\n            itemStyle: {\n              color: 'rgba(78, 205, 196, 0.8)'\n            }\n          },\n          {\n            name: '民盟',\n            value: 14,\n            itemStyle: {\n              color: 'rgba(69, 183, 209, 0.8)'\n            }\n          }\n        ]\n      }\n\n      // 预定义颜色数组\n      const colors = [\n        'rgba(255, 107, 107, 0.8)', 'rgba(78, 205, 196, 0.8)', 'rgba(69, 183, 209, 0.8)',\n        'rgba(150, 206, 180, 0.8)', 'rgba(255, 234, 167, 0.8)', 'rgba(221, 160, 221, 0.8)',\n        'rgba(152, 216, 200, 0.8)', 'rgba(247, 220, 111, 0.8)', 'rgba(187, 143, 206, 0.8)',\n        'rgba(133, 193, 233, 0.8)'\n      ]\n\n      return this.chartData.map((item, index) => ({\n        name: item.name,\n        value: item.value,\n        itemStyle: {\n          color: colors[index % colors.length]\n        }\n      }))\n    },\n    // 配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比\n    getPie3D (pieData, internalDiameterRatio) {\n      const that = this\n      const series = []\n      let sumValue = 0\n      let startValue = 0\n      let endValue = 0\n      let legendData = []\n      let legendBfb = []\n      const k = 1 - internalDiameterRatio\n      pieData.sort((a, b) => {\n        return (b.value - a.value)\n      })\n\n      // 标记前三名，用于显示线条指示\n      pieData.forEach((item, index) => {\n        item.isTopThree = index < 3\n        item.rank = index + 1\n      })\n\n      // 调试输出数据顺序\n      console.log('3D饼图数据顺序:', pieData.map(item => `${item.name}: ${item.value}`))\n      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置\n      for (let i = 0; i < pieData.length; i++) {\n        sumValue += pieData[i].value\n        const seriesItem = {\n          // 系统名称\n          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,\n          type: 'surface',\n          // 是否为参数曲面（是）\n          parametric: true,\n          // 曲面图网格线（否）上面一根一根的\n          wireframe: {\n            show: false\n          },\n          pieData: pieData[i],\n          pieStatus: {\n            selected: false,\n            hovered: false,\n            k: k\n          },\n          // 设置饼图在容器中的位置(目前没发现啥用)\n          center: ['80%', '100%'],\n          radius: '60%'\n        }\n\n        // 曲面的颜色、不透明度等样式。\n        if (typeof pieData[i].itemStyle !== 'undefined') {\n          const itemStyle = {}\n          if (typeof pieData[i].itemStyle.color !== 'undefined') {\n            itemStyle.color = pieData[i].itemStyle.color\n          }\n          if (typeof pieData[i].itemStyle.opacity !== 'undefined') {\n            itemStyle.opacity = pieData[i].itemStyle.opacity\n          }\n          seriesItem.itemStyle = itemStyle\n        }\n        series.push(seriesItem)\n      }\n\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\n      legendData = []\n      legendBfb = []\n      for (let i = 0; i < series.length; i++) {\n        endValue = startValue + series[i].pieData.value\n        series[i].pieData.startRatio = startValue / sumValue\n        series[i].pieData.endRatio = endValue / sumValue\n        series[i].parametricEquation = that.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,\n          false, false, k, series[i].pieData.value)\n        startValue = endValue\n        const bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4)\n        legendData.push({\n          name: series[i].name,\n          value: bfb\n        })\n        legendBfb.push({\n          name: series[i].name,\n          value: bfb\n        })\n      }\n\n      // (第二个参数可以设置你这个环形的高低程度)\n      const boxHeight = this.getHeight3D(series, 20) // 通过传参设定3d饼/环的高度\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\n      const option = {\n        // 图例组件\n        legend: {\n          data: legendData,\n          // 图例列表的布局朝向 - 垂直布局\n          orient: 'vertical',\n          right: '5%', // 距离右边5%\n          top: 'center', // 垂直居中\n          itemWidth: 14,\n          itemHeight: 14,\n          itemGap: 30, // 增加间距\n          textStyle: {\n            color: '#A1E2FF',\n            fontSize: 13\n          },\n          // 简化格式化，只显示名称和百分比\n          formatter: function (param) {\n            const item = legendBfb.filter(item => item.name === param)[0]\n            const bfs = that.fomatFloat(item.value * 100, 1) + '%'\n            return `${item.name}: ${bfs}`\n          }\n        },\n        // 移动上去提示的文本内容\n        tooltip: {\n          formatter: params => {\n            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n              const bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) *\n                100).toFixed(2)\n              return `${params.seriesName}<br/>` +\n                `<span style=\"display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>` +\n                `${bfb}%`\n            }\n          }\n        },\n        // 这个可以变形\n        xAxis3D: {\n          min: -1,\n          max: 1\n        },\n        yAxis3D: {\n          min: -1,\n          max: 1\n        },\n        zAxis3D: {\n          min: -1,\n          max: 1\n        },\n        // 此处是修改样式的重点\n        grid3D: {\n          show: false,\n          boxHeight: boxHeight, // 圆环的高度\n          // 这是饼图的位置 - 调整到合适位置\n          top: '-5%', // 适中的垂直位置\n          left: '-8%', // 适中的水平位置\n          width: '60%', // 限制图表宽度，为右侧图例留出空间\n          // 优化性能配置\n          postEffect: {\n            enable: false // 关闭后处理效果，提升性能\n          },\n          light: {\n            main: {\n              intensity: 1.2,\n              shadow: false // 关闭阴影，提升性能\n            },\n            ambient: {\n              intensity: 0.3\n            }\n          },\n          viewControl: { // 3d效果可以放大、旋转等，请自己去查看官方配置\n            alpha: 25, // 适中的俯视角度\n            beta: 0, // 水平旋转角度\n            distance: 150, // 适中的观察距离\n            center: [0, 0, 0], // 3D场景中心点\n            rotateSensitivity: 0, // 设置为0无法旋转\n            zoomSensitivity: 0, // 设置为0无法缩放\n            panSensitivity: 0, // 设置为0无法平移\n            autoRotate: false, // 自动旋转\n            animation: false // 关闭动画，提升性能\n          }\n        },\n        series: [\n          ...series\n          // 添加前三名的线条指示\n          // {\n          //   type: 'pie',\n          //   radius: ['30%', '60%'], // 调整半径以匹配3D饼图的视觉范围\n          //   center: ['30%', '45%'], // 微调中心点以更精确匹配3D饼图\n          //   startAngle: 30, // 进一步调整起始角度\n          //   clockwise: false, // 顺时针方向\n          //   // 使用完整数据以确保角度对应正确，但只为前三名设置标签\n          //   data: pieData.map(item => ({\n          //     name: item.name,\n          //     value: item.value,\n          //     itemStyle: {\n          //       color: 'transparent' // 透明，只显示标签线\n          //     },\n          //     label: {\n          //       show: item.isTopThree, // 只有前三名显示标签\n          //       position: 'outside',\n          //       fontSize: 12,\n          //       color: '#FFFFFF',\n          //       fontWeight: 'bold',\n          //       backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //       borderColor: '#A1E2FF',\n          //       borderWidth: 1,\n          //       borderRadius: 4,\n          //       padding: [3, 6],\n          //       formatter: `${item.name} ${item.value}人`\n          //     },\n          //     labelLine: {\n          //       show: item.isTopThree, // 只有前三名显示线条\n          //       length: 25, // 适中的第一段线条长度\n          //       length2: 15, // 适中的第二段线条长度\n          //       lineStyle: {\n          //         color: '#A1E2FF',\n          //         width: 2,\n          //         type: 'solid'\n          //       }\n          //     }\n          //   })),\n          //   // 全局标签配置（会被数据项中的配置覆盖）\n          //   label: {\n          //     show: false, // 默认不显示\n          //     position: 'outside',\n          //     fontSize: 12,\n          //     color: '#FFFFFF',\n          //     fontWeight: 'bold',\n          //     backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //     borderColor: '#A1E2FF',\n          //     borderWidth: 1,\n          //     borderRadius: 4,\n          //     padding: [3, 6],\n          //     formatter: function (params) {\n          //       return `${params.name} ${params.value}人`\n          //     }\n          //   },\n          //   labelLine: {\n          //     show: false, // 默认不显示\n          //     length: 25,\n          //     length2: 15,\n          //     lineStyle: {\n          //       color: '#A1E2FF',\n          //       width: 2,\n          //       type: 'solid'\n          //     }\n          //   },\n          //   silent: true,\n          //   z: 10\n          // }\n        ]\n      }\n      return option\n    },\n    // 获取3d饼图的最高扇区的高度\n    getHeight3D (series, height) {\n      series.sort((a, b) => {\n        return (b.pieData.value - a.pieData.value)\n      })\n      return height * 25 / series[0].pieData.value\n    },\n\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\n    getParametricEquation (startRatio, endRatio, isSelected, isHovered, k, h) {\n      // 计算\n      const midRatio = (startRatio + endRatio) / 2\n      const startRadian = startRatio * Math.PI * 2\n      const endRadian = endRatio * Math.PI * 2\n      const midRadian = midRatio * Math.PI * 2\n      // 如果只有一个扇形，则不实现选中效果。\n      if (startRatio === 0 && endRatio === 1) {\n        isSelected = false\n      }\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\n      k = typeof k !== 'undefined' ? k : 1 / 3\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\n      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0\n      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\n      const hoverRate = isHovered ? 1.05 : 1\n      // 返回曲面参数方程\n      return {\n        u: {\n          min: -Math.PI,\n          max: Math.PI * 3,\n          step: Math.PI / 16\n        },\n        v: {\n          min: 0,\n          max: Math.PI * 2,\n          step: Math.PI / 10\n        },\n        x: function (u, v) {\n          if (u < startRadian) {\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        y: function (u, v) {\n          if (u < startRadian) {\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        z: function (u, v) {\n          if (u < -Math.PI * 0.5) {\n            return Math.sin(u)\n          }\n          if (u > Math.PI * 2.5) {\n            return Math.sin(u) * h * 0.1\n          }\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1\n        }\n      }\n    },\n\n    // 防抖函数\n    debounce (func, wait) {\n      let timeout\n      return function executedFunction (...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 优化的更新图表方法\n    updateChartOption (option) {\n      if (this.isAnimating) return\n      this.isAnimating = true\n\n      // 使用requestAnimationFrame优化渲染\n      requestAnimationFrame(() => {\n        if (this.chart) {\n          this.chart.setOption(option, false, false) // 关闭动画和合并\n        }\n        this.isAnimating = false\n      })\n    },\n\n    // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。\n    bindListen (myChart) {\n      const that = this\n      let selectedIndex = ''\n      let hoveredIndex = ''\n      // 监听点击事件，实现选中效果（单选）\n      myChart.on('click', function (params) {\n        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。\n        const isSelected = !that.option.series[params.seriesIndex].pieStatus.selected\n        const isHovered = that.option.series[params.seriesIndex].pieStatus.hovered\n        const k = that.option.series[params.seriesIndex].pieStatus.k\n        const startRatio = that.option.series[params.seriesIndex].pieData.startRatio\n        const endRatio = that.option.series[params.seriesIndex].pieData.endRatio\n        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）\n        if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {\n          that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(\n            that.option.series[selectedIndex].pieData.startRatio,\n            that.option.series[selectedIndex].pieData.endRatio,\n            false,\n            false,\n            k,\n            that.option.series[selectedIndex].pieData.value\n          )\n          that.option.series[selectedIndex].pieStatus.selected = false\n        }\n        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）\n        that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,\n          isSelected,\n          isHovered, k, that.option.series[params.seriesIndex].pieData.value)\n        that.option.series[params.seriesIndex].pieStatus.selected = isSelected\n        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex\n        if (isSelected) {\n          selectedIndex = params.seriesIndex\n        }\n        // 使用优化的更新方法\n        that.updateChartOption(that.option)\n      })\n\n      // 防抖的mouseover处理\n      const debouncedMouseover = this.debounce((params) => {\n        that.handleMouseover(params, hoveredIndex, (newIndex) => {\n          hoveredIndex = newIndex\n        })\n      }, 16) // 约60fps\n\n      // 监听 mouseover，近似实现高亮（放大）效果\n      myChart.on('mouseover', debouncedMouseover)\n\n      // 修正取消高亮失败的 bug\n      myChart.on('globalout', function () {\n        if (hoveredIndex !== '') {\n          const isSelected = that.option.series[hoveredIndex].pieStatus.selected\n          const isHovered = false\n          const k = that.option.series[hoveredIndex].pieStatus.k\n          const startRatio = that.option.series[hoveredIndex].pieData.startRatio\n          const endRatio = that.option.series[hoveredIndex].pieData.endRatio\n\n          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(\n            startRatio, endRatio, isSelected, isHovered, k,\n            that.option.series[hoveredIndex].pieData.value\n          )\n          that.option.series[hoveredIndex].pieStatus.hovered = isHovered\n          hoveredIndex = ''\n\n          // 使用优化的更新方法\n          that.updateChartOption(that.option)\n        }\n      })\n    },\n\n    // 提取mouseover处理逻辑\n    handleMouseover (params, hoveredIndex, setHoveredIndex) {\n      // 如果触发 mouseover 的扇形当前已高亮，则不做操作\n      if (hoveredIndex === params.seriesIndex) {\n        return // 已经高亮，不做操作\n      }\n\n      // 准备重新渲染扇形所需的参数\n      let isSelected, isHovered, startRatio, endRatio, k\n\n      // 如果当前有高亮的扇形，取消其高亮状态\n      if (hoveredIndex !== '') {\n        isSelected = this.option.series[hoveredIndex].pieStatus.selected\n        isHovered = false\n        startRatio = this.option.series[hoveredIndex].pieData.startRatio\n        endRatio = this.option.series[hoveredIndex].pieData.endRatio\n        k = this.option.series[hoveredIndex].pieStatus.k\n\n        this.option.series[hoveredIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[hoveredIndex].pieData.value\n        )\n        this.option.series[hoveredIndex].pieStatus.hovered = isHovered\n      }\n\n      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮\n      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n        isSelected = this.option.series[params.seriesIndex].pieStatus.selected\n        isHovered = true\n        startRatio = this.option.series[params.seriesIndex].pieData.startRatio\n        endRatio = this.option.series[params.seriesIndex].pieData.endRatio\n        k = this.option.series[params.seriesIndex].pieStatus.k\n\n        this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[params.seriesIndex].pieData.value + 5\n        )\n        this.option.series[params.seriesIndex].pieStatus.hovered = isHovered\n        setHoveredIndex(params.seriesIndex)\n      }\n\n      // 使用优化的更新方法\n      this.updateChartOption(this.option)\n    },\n\n    // 这是一个自定义计算的方法\n    fomatFloat (num, n) {\n      var f = parseFloat(num)\n      if (isNaN(f)) {\n        return false\n      }\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂\n      var s = f.toString()\n      var rs = s.indexOf('.')\n      // 判定如果是整数，增加小数点再补0\n      if (rs < 0) {\n        rs = s.length\n        s += '.'\n      }\n      while (s.length <= rs + n) {\n        s += '0'\n      }\n      return s\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.party-distribution-chart {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图(外面的容器)\n.container {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图的大小\n.chartsGl {\n  height: 100%;\n  width: 100%;\n}\n</style>\n"]}]}