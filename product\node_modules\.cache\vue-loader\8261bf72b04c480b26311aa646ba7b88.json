{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1758775922726}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgeyB1c2VJbmRleCB9IGZyb20gJy4uL3NjcmVlbi5qcycNCmltcG9ydCBCYXJTY3JvbGxDaGFydCBmcm9tICcuLi9jb21wb25lbnRzL0JhclNjcm9sbENoYXJ0LnZ1ZScNCmltcG9ydCBCYXJDaGFydCBmcm9tICcuLi9jb21wb25lbnRzL0JhckNoYXJ0LnZ1ZScNCmltcG9ydCBQaWVDaGFydDNEIGZyb20gJy4uL2NvbXBvbmVudHMvUGllQ2hhcnQzRC52dWUnDQppbXBvcnQgUGllQ2hhcnQgZnJvbSAnLi4vY29tcG9uZW50cy9QaWVDaGFydC52dWUnDQppbXBvcnQgSG9yaXpvbnRhbEJhckNoYXJ0IGZyb20gJy4uL2NvbXBvbmVudHMvSG9yaXpvbnRhbEJhckNoYXJ0LnZ1ZScNCmltcG9ydCBHZW5kZXJSYXRpb0NoYXJ0IGZyb20gJy4uL2NvbXBvbmVudHMvR2VuZGVyUmF0aW9DaGFydC52dWUnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0JpZ1NjcmVlbicsDQogIGNvbXBvbmVudHM6IHsNCiAgICBCYXJTY3JvbGxDaGFydCwNCiAgICBCYXJDaGFydCwNCiAgICBQaWVDaGFydDNELA0KICAgIFBpZUNoYXJ0LA0KICAgIEhvcml6b250YWxCYXJDaGFydCwNCiAgICBHZW5kZXJSYXRpb0NoYXJ0DQogIH0sDQogIGRhdGEgKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50VGltZTogJycsDQogICAgICAvLyDlnLDljLrpgInmi6nnm7jlhbMNCiAgICAgIHNob3dBcmVhUG9wb3ZlcjogZmFsc2UsDQogICAgICBzZWxlY3RlZEFyZWE6ICfpnZLlspsnLA0KICAgICAgc2VsZWN0ZWREaXN0cmljdENvZGU6ICcnLA0KICAgICAgdHJlZVByb3BzOiB7DQogICAgICAgIGNoaWxkcmVuOiAnY2hpbGRyZW4nLA0KICAgICAgICBsYWJlbDogJ25hbWUnDQogICAgICB9LA0KICAgICAgdHJlZURhdGE6IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfpnZLlspsnLA0KICAgICAgICAgIGNvZGU6ICdxaW5nZGFvJywNCiAgICAgICAgICBjaGlsZHJlbjogWw0KICAgICAgICAgICAgeyBuYW1lOiAn5biC5Y2X5Yy6JywgY29kZTogJ3NoaW5hbicgfSwNCiAgICAgICAgICAgIHsgbmFtZTogJ+W4guWMl+WMuicsIGNvZGU6ICdzaGliZWknIH0sDQogICAgICAgICAgICB7IG5hbWU6ICfmnY7msqfljLonLCBjb2RlOiAnbGljYW5nJyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5bSC5bGx5Yy6JywgY29kZTogJ2xhb3NoYW4nIH0sDQogICAgICAgICAgICB7IG5hbWU6ICfln47pmLPljLonLCBjb2RlOiAnY2hlbmd5YW5nJyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn5Y2z5aKo5Yy6JywgY29kZTogJ2ppbW8nIH0sDQogICAgICAgICAgICB7IG5hbWU6ICfog7blt57luIInLCBjb2RlOiAnamlhb3pob3UnIH0sDQogICAgICAgICAgICB7IG5hbWU6ICflubPluqbluIInLCBjb2RlOiAncGluZ2R1JyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn6I6x6KW/5biCJywgY29kZTogJ2xhaXhpJyB9LA0KICAgICAgICAgICAgeyBuYW1lOiAn6KW/5rW35bK45paw5Yy6JywgY29kZTogJ3hpaGFpYW4nIH0NCiAgICAgICAgICBdDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBtZW1iZXJUb3RhbE51bTogMCwNCiAgICAgIHN0YW5kaW5nTWVtYmVyVG90YWxOdW06IDAsDQogICAgICBtYWxlOiAwLA0KICAgICAgd29tYW46IDAsDQogICAgICBnZW5kZXJTaG93OiBmYWxzZSwNCiAgICAgIC8vIOWtpuWOhuaVsOaNrg0KICAgICAgZWR1Y2F0aW9uRGF0YTogW10sDQogICAgICAvLyDlhZrmtL7mlbDmja4NCiAgICAgIHBhcnR5RGF0YTogW10sDQogICAgICAvLyDnlYzliKvliIbmnpDmlbDmja4NCiAgICAgIHNlY3RvckFuYWx5c2lzRGF0YTogWw0KICAgICAgICAvLyB7IG5hbWU6ICfnu4/mtY7nlYwnLCB2YWx1ZTogMzIgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5pWZ6IKy55WMJywgdmFsdWU6IDE1IH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+enkeaKgOeVjCcsIHZhbHVlOiAxNCB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflt6XllYbnlYwnLCB2YWx1ZTogMTMgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5Yy76I2v5Y2r55Sf55WMJywgdmFsdWU6IDEyIH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+ekvuS8muenkeWtpueVjCcsIHZhbHVlOiAxMCB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflt6XkvJonLCB2YWx1ZTogOCB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflhbHpnZLlm6InLCB2YWx1ZTogNyB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflpofogZQnLCB2YWx1ZTogNiB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfnp5HljY8nLCB2YWx1ZTogNSB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflj7DogZQnLCB2YWx1ZTogNyB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfkvqjogZQnLCB2YWx1ZTogMyB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfmlofljJboibrmnK/nlYwnLCB2YWx1ZTogMjQgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5L2T6IKy55WMJywgdmFsdWU6IDE2IH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+WwkeaVsOawkeaXj+eVjCcsIHZhbHVlOiAyMCB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflrpfmlZnnlYwnLCB2YWx1ZTogMjcgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn54m56YKA5Lq65aOrJywgdmFsdWU6IDIxIH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+a4r+a+s+WPsOS+qCcsIHZhbHVlOiA1IH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+WvueWkluWPi+WlveeVjCcsIHZhbHVlOiAxOSB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfnpL7kvJrnpo/liKnlkoznpL7kvJrkv53pmpznlYwnLCB2YWx1ZTogMTIgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn56S+5Lya5rK755CG5ZKM56S+5Lya57uE57uH55WMJywgdmFsdWU6IDIxIH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+WMu+iNr+WNq+eUn+eVjCcsIHZhbHVlOiAxMiB9LA0KICAgICAgICAvLyB7IG5hbWU6ICfnpL7kvJrnp5HlrabnlYwnLCB2YWx1ZTogMTAgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5bel5LyaJywgdmFsdWU6IDggfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5YWx6Z2S5ZuiJywgdmFsdWU6IDcgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5aaH6IGUJywgdmFsdWU6IDYgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn56eR5Y2PJywgdmFsdWU6IDUgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5Y+w6IGUJywgdmFsdWU6IDcgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn5L2T6IKy55WMJywgdmFsdWU6IDE2IH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+WwkeaVsOawkeaXj+eVjCcsIHZhbHVlOiAyMCB9LA0KICAgICAgICAvLyB7IG5hbWU6ICflrpfmlZnnlYwnLCB2YWx1ZTogMjcgfSwNCiAgICAgICAgLy8geyBuYW1lOiAn54m56YKA5Lq65aOrJywgdmFsdWU6IDIxIH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+a4r+a+s+WPsOS+qCcsIHZhbHVlOiA1IH0sDQogICAgICAgIC8vIHsgbmFtZTogJ+WvueWkluWPi+WlveeVjCcsIHZhbHVlOiAxOSB9DQogICAgICBdLA0KICAgICAgLy8g6K6o6K6657uE5Lq65ZGY57uf6K6h5pWw5o2uDQogICAgICBkaXNjdXNzaW9uR3JvdXBEYXRhOiBbXSwNCiAgICAgIC8vIOW5tOm+hOaVsOaNrg0KICAgICAgYWdlQ2hhcnREYXRhOiBbXSwNCiAgICAgIGFnZUNoYXJ0TmFtZTogJ+W5tOm+hOWNoOavlCcNCiAgICB9DQogIH0sDQogIGNvbXB1dGVkOiB7DQogIH0sDQogIG1vdW50ZWQgKCkgew0KICAgIHRoaXMuaW5pdFNjcmVlbigpDQogICAgdGhpcy51cGRhdGVUaW1lKCkNCiAgICB0aGlzLnRpbWVJbnRlcnZhbCA9IHNldEludGVydmFsKHRoaXMudXBkYXRlVGltZSwgMTAwMCkNCiAgICB0aGlzLmdldERhdGEoKQ0KICB9LA0KICBiZWZvcmVEZXN0cm95ICgpIHsNCiAgICBpZiAodGhpcy50aW1lSW50ZXJ2YWwpIHsNCiAgICAgIGNsZWFySW50ZXJ2YWwodGhpcy50aW1lSW50ZXJ2YWwpDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaW5pdFNjcmVlbiAoKSB7DQogICAgICBjb25zdCB7IGNhbGNSYXRlLCB3aW5kb3dEcmF3IH0gPSB1c2VJbmRleCh0aGlzLiRyZWZzLmJpZ1NjcmVlbikNCiAgICAgIGNhbGNSYXRlKCkNCiAgICAgIHdpbmRvd0RyYXcoKQ0KICAgIH0sDQogICAgdXBkYXRlVGltZSAoKSB7DQogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpDQogICAgICB0aGlzLmN1cnJlbnRUaW1lID0gbm93LnRvTG9jYWxlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgeWVhcjogJ251bWVyaWMnLA0KICAgICAgICBtb250aDogJzItZGlnaXQnLA0KICAgICAgICBkYXk6ICcyLWRpZ2l0JywNCiAgICAgICAgaG91cjogJzItZGlnaXQnLA0KICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JywNCiAgICAgICAgc2Vjb25kOiAnMi1kaWdpdCcNCiAgICAgIH0pDQogICAgfSwNCiAgICBnZXREYXRhICgpIHsNCiAgICAgIHRoaXMuZ2V0Q29tbWl0dGVlTWVtYmVyc051bWJlcigpDQogICAgICB0aGlzLmdldENvbW1pdHRlZU1lbWJlcnNTZXgoKQ0KICAgICAgdGhpcy5nZXRDb21taXR0ZWVNZW1iZXJzQWdlKCkNCiAgICAgIHRoaXMuZ2V0Q29tbWl0dGVlTWVtYmVyc0VkdWNhdGlvbigpDQogICAgICB0aGlzLmdldENvbW1pdHRlZU1lbWJlcnNQYXJ0eSgpDQogICAgICB0aGlzLmdldENvbW1pdHRlZU1lbWJlcnNHcm91cCgpDQogICAgICB0aGlzLmdldENvbW1pdHRlZU1lbWJlcnNTZWN0b3IoKQ0KICAgIH0sDQogICAgLy8g6I635Y+W5aeU5ZGY5pWw6YePDQogICAgYXN5bmMgZ2V0Q29tbWl0dGVlTWVtYmVyc051bWJlciAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuc21hcnRCcmFpbkxhcmdlU2NyZWVuLm1lbWJlclBhcnR5U3RhdHMoKQ0KICAgICAgdGhpcy5tZW1iZXJUb3RhbE51bSA9IHJlcy5kYXRhLnRvdGFsQ291bnQNCiAgICAgIHRoaXMuc3RhbmRpbmdNZW1iZXJUb3RhbE51bSA9IHJlcy5kYXRhLnJvdXRpbmVDb3VudA0KICAgIH0sDQogICAgLy8g6I635Y+W5oCn5YirDQogICAgYXN5bmMgZ2V0Q29tbWl0dGVlTWVtYmVyc1NleCAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuc21hcnRCcmFpbkxhcmdlU2NyZWVuLm1lbWJlckFuYWx5c2lzKHsgdHlwZTogJ2dlbmRlcicgfSkNCiAgICAgIHRoaXMubWFsZSA9IE51bWJlcihyZXMuZGF0YVsxXS5yYXRpbykNCiAgICAgIHRoaXMud29tYW4gPSBOdW1iZXIocmVzLmRhdGFbMF0ucmF0aW8pDQogICAgICB0aGlzLmdlbmRlclNob3cgPSB0cnVlDQogICAgfSwNCiAgICAvLyDojrflj5blubTpvoQNCiAgICBhc3luYyBnZXRDb21taXR0ZWVNZW1iZXJzQWdlICgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5zbWFydEJyYWluTGFyZ2VTY3JlZW4ubWVtYmVyQW5hbHlzaXMoeyB0eXBlOiAnYWdlJyB9KQ0KICAgICAgdGhpcy5hZ2VDaGFydERhdGEgPSByZXMuZGF0YQ0KICAgIH0sDQogICAgLy8g6I635Y+W5a2m5Y6GDQogICAgYXN5bmMgZ2V0Q29tbWl0dGVlTWVtYmVyc0VkdWNhdGlvbiAoKSB7DQogICAgICBjb25zdCByZXMgPSBhd2FpdCB0aGlzLiRhcGkuc21hcnRCcmFpbkxhcmdlU2NyZWVuLm1lbWJlckFuYWx5c2lzKHsgdHlwZTogJ2VkdWNhdGlvbicgfSkNCiAgICAgIHRoaXMuZWR1Y2F0aW9uRGF0YSA9IHJlcy5kYXRhDQogICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgdGhpcy5zdGFydEF1dG9TY3JvbGwoKQ0KICAgICAgfSwgODAwKQ0KICAgIH0sDQogICAgLy8g6I635Y+W5YWa5rS+DQogICAgYXN5bmMgZ2V0Q29tbWl0dGVlTWVtYmVyc1BhcnR5ICgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5zbWFydEJyYWluTGFyZ2VTY3JlZW4ubWVtYmVyQW5hbHlzaXMoeyB0eXBlOiAncGFydHknIH0pDQogICAgICB0aGlzLnBhcnR5RGF0YSA9IHJlcy5kYXRhDQogICAgfSwNCiAgICAvLyDojrflj5borqjorrrnu4TkurrlkZjnu5/orqENCiAgICBhc3luYyBnZXRDb21taXR0ZWVNZW1iZXJzR3JvdXAgKCkgew0KICAgICAgY29uc3QgcmVzID0gYXdhaXQgdGhpcy4kYXBpLnNtYXJ0QnJhaW5MYXJnZVNjcmVlbi5tZW1iZXJBbmFseXNpcyh7IHR5cGU6ICdncm91cCcgfSkNCiAgICAgIHRoaXMuZGlzY3Vzc2lvbkdyb3VwRGF0YSA9IHJlcy5kYXRhDQogICAgfSwNCiAgICAvLyDojrflj5bnlYzliKvliIbluIMNCiAgICBhc3luYyBnZXRDb21taXR0ZWVNZW1iZXJzU2VjdG9yICgpIHsNCiAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IHRoaXMuJGFwaS5zbWFydEJyYWluTGFyZ2VTY3JlZW4ubWVtYmVyQW5hbHlzaXMoeyB0eXBlOiAnc2VjdG9yJyB9KQ0KICAgICAgdGhpcy5zZWN0b3JBbmFseXNpc0RhdGEgPSByZXMuZGF0YQ0KICAgIH0sDQogICAgLy8g6L+U5Zue6aaW6aG1DQogICAgZ29Ib21lICgpIHsNCiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsgcGF0aDogJy9ob21lQm94JyB9KQ0KICAgIH0sDQogICAgLy8g5aSE55CG5qCR6IqC54K554K55Ye7DQogICAgaGFuZGxlTm9kZUNsaWNrIChkYXRhLCBub2RlKSB7DQogICAgICAvLyDlhYHorrjpgInmi6nmiYDmnInoioLngrnvvIjljIXmi6zniLbnuqfpnZLlspvvvIkNCiAgICAgIHRoaXMuc2VsZWN0ZWRBcmVhID0gZGF0YS5uYW1lDQogICAgICB0aGlzLnNlbGVjdGVkRGlzdHJpY3RDb2RlID0gZGF0YS5jb2RlDQogICAgICB0aGlzLnNob3dBcmVhUG9wb3ZlciA9IGZhbHNlDQogICAgICAvLyDov5nph4zlj6/ku6Xmt7vliqDliIfmjaLlnLDljLrlkI7nmoTmlbDmja7mm7TmlrDpgLvovpENCiAgICAgIGNvbnNvbGUubG9nKCfpgInmi6nkuoblnLDljLo6JywgZGF0YSkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["committeeStatisticsBox.vue"], "names": [], "mappings": ";AA2HA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "committeeStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <el-popover placement=\"bottom\" width=\"280\" trigger=\"click\" popper-class=\"area-popover\"\r\n            v-model=\"showAreaPopover\">\r\n            <el-scrollbar class=\"region-tree\">\r\n              <el-tree :data=\"treeData\" :props=\"treeProps\" node-key=\"code\" :default-expanded-keys=\"['qingdao']\"\r\n                :current-node-key=\"selectedDistrictCode\" @node-click=\"handleNodeClick\" class=\"area-tree\">\r\n              </el-tree>\r\n            </el-scrollbar>\r\n            <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n              <span>{{ selectedArea }}</span>\r\n              <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n            </div>\r\n          </el-popover>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" v-if=\"genderShow\" :male-ratio=\"male\" :female-ratio=\"woman\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" v-if=\"ageChartData.length > 0\" :chart-data=\"ageChartData\" :name=\"ageChartName\"\r\n              legendIcon=\"circle\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" v-if=\"partyData.length > 0\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      treeData: [\r\n        {\r\n          name: '青岛',\r\n          code: 'qingdao',\r\n          children: [\r\n            { name: '市南区', code: 'shinan' },\r\n            { name: '市北区', code: 'shibei' },\r\n            { name: '李沧区', code: 'licang' },\r\n            { name: '崂山区', code: 'laoshan' },\r\n            { name: '城阳区', code: 'chengyang' },\r\n            { name: '即墨区', code: 'jimo' },\r\n            { name: '胶州市', code: 'jiaozhou' },\r\n            { name: '平度市', code: 'pingdu' },\r\n            { name: '莱西市', code: 'laixi' },\r\n            { name: '西海岸新区', code: 'xihaian' }\r\n          ]\r\n        }\r\n      ],\r\n      memberTotalNum: 0,\r\n      standingMemberTotalNum: 0,\r\n      male: 0,\r\n      woman: 0,\r\n      genderShow: false,\r\n      // 学历数据\r\n      educationData: [],\r\n      // 党派数据\r\n      partyData: [],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        // { name: '经济界', value: 32 },\r\n        // { name: '教育界', value: 15 },\r\n        // { name: '科技界', value: 14 },\r\n        // { name: '工商界', value: 13 },\r\n        // { name: '医药卫生界', value: 12 },\r\n        // { name: '社会科学界', value: 10 },\r\n        // { name: '工会', value: 8 },\r\n        // { name: '共青团', value: 7 },\r\n        // { name: '妇联', value: 6 },\r\n        // { name: '科协', value: 5 },\r\n        // { name: '台联', value: 7 },\r\n        // { name: '侨联', value: 3 },\r\n        // { name: '文化艺术界', value: 24 },\r\n        // { name: '体育界', value: 16 },\r\n        // { name: '少数民族界', value: 20 },\r\n        // { name: '宗教界', value: 27 },\r\n        // { name: '特邀人士', value: 21 },\r\n        // { name: '港澳台侨', value: 5 },\r\n        // { name: '对外友好界', value: 19 },\r\n        // { name: '社会福利和社会保障界', value: 12 },\r\n        // { name: '社会治理和社会组织界', value: 21 },\r\n        // { name: '医药卫生界', value: 12 },\r\n        // { name: '社会科学界', value: 10 },\r\n        // { name: '工会', value: 8 },\r\n        // { name: '共青团', value: 7 },\r\n        // { name: '妇联', value: 6 },\r\n        // { name: '科协', value: 5 },\r\n        // { name: '台联', value: 7 },\r\n        // { name: '体育界', value: 16 },\r\n        // { name: '少数民族界', value: 20 },\r\n        // { name: '宗教界', value: 27 },\r\n        // { name: '特邀人士', value: 21 },\r\n        // { name: '港澳台侨', value: 5 },\r\n        // { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [],\r\n      // 年龄数据\r\n      ageChartData: [],\r\n      ageChartName: '年龄占比'\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n    this.getData()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getData () {\r\n      this.getCommitteeMembersNumber()\r\n      this.getCommitteeMembersSex()\r\n      this.getCommitteeMembersAge()\r\n      this.getCommitteeMembersEducation()\r\n      this.getCommitteeMembersParty()\r\n      this.getCommitteeMembersGroup()\r\n      this.getCommitteeMembersSector()\r\n    },\r\n    // 获取委员数量\r\n    async getCommitteeMembersNumber () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberPartyStats()\r\n      this.memberTotalNum = res.data.totalCount\r\n      this.standingMemberTotalNum = res.data.routineCount\r\n    },\r\n    // 获取性别\r\n    async getCommitteeMembersSex () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'gender' })\r\n      this.male = Number(res.data[1].ratio)\r\n      this.woman = Number(res.data[0].ratio)\r\n      this.genderShow = true\r\n    },\r\n    // 获取年龄\r\n    async getCommitteeMembersAge () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'age' })\r\n      this.ageChartData = res.data\r\n    },\r\n    // 获取学历\r\n    async getCommitteeMembersEducation () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'education' })\r\n      this.educationData = res.data\r\n      setTimeout(() => {\r\n        this.startAutoScroll()\r\n      }, 800)\r\n    },\r\n    // 获取党派\r\n    async getCommitteeMembersParty () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'party' })\r\n      this.partyData = res.data\r\n    },\r\n    // 获取讨论组人员统计\r\n    async getCommitteeMembersGroup () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'group' })\r\n      this.discussionGroupData = res.data\r\n    },\r\n    // 获取界别分布\r\n    async getCommitteeMembersSector () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'sector' })\r\n      this.sectorAnalysisData = res.data\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: 220px;\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式\r\n.area-popover {\r\n  width: 290px !important;\r\n  padding: 0 !important;\r\n\r\n  .region-tree {\r\n    width: 100%;\r\n\r\n    ::v-deep .el-tree-node {\r\n      .el-tree-node__content {\r\n        height: 40px;\r\n        line-height: 40px;\r\n\r\n        .el-tree-node__label {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n        }\r\n      }\r\n\r\n      // 当前选中节点\r\n      &.is-current>.el-tree-node__content {\r\n        background: #ebeef9;\r\n        color: #3657C0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}