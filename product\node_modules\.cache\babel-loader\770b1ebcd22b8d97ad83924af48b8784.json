{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1758769074724}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA+GA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC;EADA,CAFA;;EAKAC;IACA;MACAC,eADA;MAEA;MACAC,mBAHA;MAIA;MACAC,oBALA;MAMA;MACAC,kBACA;QAAAP;QAAAQ;MAAA,CADA,EAEA;QAAAR;QAAAQ;MAAA,CAFA,EAGA;QAAAR;QAAAQ;MAAA,CAHA,EAIA;QAAAR;QAAAQ;MAAA,CAJA,CAPA;MAaA;MACAC,yBACA;QAAAT;QAAAQ;QAAAE;MAAA,CADA,EAEA;QAAAV;QAAAQ;QAAAE;MAAA,CAFA,EAGA;QAAAV;QAAAQ;QAAAE;MAAA,CAHA,EAIA;QAAAV;QAAAQ;QAAAE;MAAA,CAJA,EAKA;QAAAV;QAAAQ;QAAAE;MAAA,CALA,EAMA;QAAAV;QAAAQ;QAAAE;MAAA,CANA;IAdA;EAuBA,CA7BA;;EA8BAC,YA9BA;;EAgCAC;IACA;IACA;IACA;IACA;EACA,CArCA;;EAsCAC;IACA;MACAC;IACA;EACA,CA1CA;;EA2CAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBAC;MACA;MACA;MACA;MACA;IACA,CAtBA;;IAuBA;IACA;MACA;MACA;IACA,CA3BA;;IA4BA;IACA;MACA;QAAAC;MAAA;MACA;IACA,CAhCA;;IAiCA;IACA;MACA;QAAAA;MAAA;MACA;MACA;MACA;MACA;IACA,CAxCA;;IAyCA;IACA;MACA;QAAAA;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAlDA;;IAmDA;IACAC;MACA;QAAAC;MAAA;IACA;;EAtDA;AA3CA", "names": ["name", "components", "BarScrollChart", "data", "currentTime", "performanceData", "activityTypeData", "meetingTypeData", "value", "performanceSummaryData", "icon", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "getData", "type", "goHome", "path"], "sourceRoot": "src/views/smartBrainLargeScreen/performanceStatistics", "sources": ["performanceStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>履职统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 年度履职汇总 -->\r\n        <div class=\"performance-summary-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年度履职汇总</span>\r\n          </div>\r\n          <div class=\"performance-summary-content\">\r\n            <div class=\"summary-grid\">\r\n              <div class=\"summary-item\" v-for=\"(item, index) in performanceSummaryData\" :key=\"index\">\r\n                <div class=\"summary-icon\">\r\n                  <img :src=\"item.icon\" :alt=\"item.name\" />\r\n                </div>\r\n                <div class=\"summary-info\">\r\n                  <div class=\"summary-label\">{{ item.name }}</div>\r\n                  <div class=\"summary-value\">{{ item.value }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 会议类型统计 -->\r\n        <div class=\"metting-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议类型统计</span>\r\n          </div>\r\n          <div class=\"metting-content\">\r\n            <div class=\"meeting-grid\">\r\n              <div class=\"meeting-item\" v-for=\"(item, index) in meetingTypeData\" :key=\"index\">\r\n                <div class=\"meeting-number\">{{ item.value }}</div>\r\n                <div class=\"meeting-label\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 活动类型统计 -->\r\n        <div class=\"activity-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">活动类型统计</span>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n            <BarScrollChart id=\"activityTypeChart\" :showCount=\"12\" :chart-data=\"activityTypeData\"\r\n              :alternate-colors=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 履职数据分析 -->\r\n        <div class=\"performance-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职数据分析</span>\r\n          </div>\r\n          <div class=\"performance-content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.userName }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.social }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.advise }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.activity }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.others }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 履职数据分析表格数据\r\n      performanceData: [],\r\n      // 活动类型统计数据\r\n      activityTypeData: [],\r\n      // 会议类型统计数据\r\n      meetingTypeData: [\r\n        { name: '全体会议', value: 0 },\r\n        { name: '常委会议', value: 0 },\r\n        { name: '主席会议', value: 0 },\r\n        { name: '其他会议', value: 0 }\r\n      ],\r\n      // 年度履职汇总数据\r\n      performanceSummaryData: [\r\n        { name: '提交提案', value: 1500, icon: require('../../../assets/largeScreen/icon_submit_proposal.png') },\r\n        { name: '提交社情民意', value: 1057, icon: require('../../../assets/largeScreen/icon_submit_social.png') },\r\n        { name: '网络议政', value: 215, icon: require('../../../assets/largeScreen/icon_network.png') },\r\n        { name: '参加会议', value: 361, icon: require('../../../assets/largeScreen/icon_metting_item.png') },\r\n        { name: '参加活动', value: 104, icon: require('../../../assets/largeScreen/icon_activity_item.png') },\r\n        { name: '其他履职', value: 241, icon: require('../../../assets/largeScreen/item_other_duties.png') }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n    this.getData()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getData () {\r\n      this.getDutyStatstics()\r\n      this.getDutyAnalysis()\r\n      this.getDutyMeetingType()\r\n      this.getDutyYearOverall()\r\n    },\r\n    // 获取履职统计\r\n    async getDutyStatstics () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyStatistics()\r\n      this.performanceData = res.data\r\n    },\r\n    // 获取活动类型统计\r\n    async getDutyAnalysis () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'activity' })\r\n      this.activityTypeData = res.data\r\n    },\r\n    // 获取会议类型统计\r\n    async getDutyMeetingType () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'meeting' })\r\n      this.meetingTypeData[0].value = res.data[6].count\r\n      this.meetingTypeData[1].value = res.data[11].count\r\n      this.meetingTypeData[2].value = res.data[1].count\r\n      this.meetingTypeData[3].value = res.data[7].count\r\n    },\r\n    // 获取年度履职汇总\r\n    async getDutyYearOverall () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'overall' })\r\n      this.performanceSummaryData[0].value = res.data[6].count\r\n      this.performanceSummaryData[1].value = res.data[11].count\r\n      this.performanceSummaryData[2].value = res.data[1].count\r\n      this.performanceSummaryData[3].value = res.data[7].count\r\n      this.performanceSummaryData[4].value = res.data[7].count\r\n      this.performanceSummaryData[5].value = res.data[7].count\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 922px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 年度履职汇总\r\n      .performance-summary-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_performance_summary_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .performance-summary-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n          display: flex;\r\n          justify-content: center;\r\n          padding-left: 20px;\r\n\r\n          .summary-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr 1fr;\r\n            gap: 12px;\r\n            width: 100%;\r\n\r\n            .summary-item {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .summary-icon {\r\n                width: 64px;\r\n                height: 64px;\r\n                margin-right: 12px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border-radius: 6px;\r\n\r\n                img {\r\n                  width: 100%;\r\n                  height: 100%;\r\n                  object-fit: contain;\r\n                }\r\n              }\r\n\r\n              .summary-info {\r\n                flex: 1;\r\n\r\n                .summary-label {\r\n                  font-weight: 400;\r\n                  font-size: 15px;\r\n                  color: #B4C0CC;\r\n                  margin-bottom: 8px;\r\n                  line-height: 20px;\r\n                }\r\n\r\n                .summary-value {\r\n                  font-family: DIN, DIN;\r\n                  font-weight: 500;\r\n                  font-size: 32px;\r\n                  color: #FFFFFF;\r\n                  line-height: 30px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 会议类型统计\r\n      .metting-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_metting_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .metting-content {\r\n          margin-top: 60px;\r\n          height: calc(100% - 70px);\r\n\r\n          .meeting-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr;\r\n            gap: 15px;\r\n            height: 100%;\r\n\r\n            .meeting-item {\r\n              width: 200px;\r\n              height: 125px;\r\n              background: url('../../../assets/largeScreen/icon_metting_type.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n              padding: 15px;\r\n              cursor: pointer;\r\n\r\n              .meeting-number {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n                font-family: 'DIN', Arial, sans-serif;\r\n                transition: all 0.3s ease;\r\n              }\r\n\r\n              .meeting-label {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                font-weight: 500;\r\n                text-align: center;\r\n                line-height: 1.2;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 活动类型统计\r\n      .activity-section {\r\n        height: 550px;\r\n        background: url('../../../assets/largeScreen/icon_activity_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）\r\n        grid-row: 2; // 第二行\r\n\r\n        .activity-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 履职数据分析\r\n      .performance-analysis-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .performance-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n\r\n          .table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid #117090;\r\n            overflow: hidden;\r\n            --name-col-width: 124px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid #117090;\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                height: 40px;\r\n                line-height: 40px;\r\n                text-align: center;\r\n                font-weight: 400;\r\n                color: #B4C0CC;\r\n                font-size: 15px;\r\n                border-right: 1px solid #117090;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid #117090;\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid #117090;\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  background: rgba(31, 198, 255, 0.16);\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(31, 198, 255, 0.16);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}