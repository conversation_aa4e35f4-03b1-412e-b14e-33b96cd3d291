{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\http.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\http.js", "mtime": 1757410438820}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "axios", "Qs", "router", "loginUc", "baseURL", "yunpan", "yiya<PERSON><PERSON>y", "dataCenter", "process", "env", "STAGE", "window", "location", "protocol", "host", "defaults", "timeout", "headers", "post", "interceptors", "request", "use", "config", "test", "url", "switchpage", "JSON", "parse", "sessionStorage", "getItem", "token", "prototype", "$logo", "scanning<PERSON>en", "tokenid", "bigDataUrl", "theme", "bigDataUrlJson", "indexOf", "clientTypeId", "urlReg", "exec", "method", "Object", "toString", "call", "data", "stringify", "filter", "params", "isOutSideNet", "hostname", "origin", "areaId", "ActiveXObject", "loginToken", "loginAreaId", "encodeURI", "qdzxtoken", "append", "console", "log", "Authorization", "error", "Promise", "reject", "response", "code", "<PERSON><PERSON><PERSON>", "message", "errmsg", "resolve", "clear", "push", "name", "undefined", "type", "includes", "param", "key", "get", "then", "res", "catch", "err", "postform", "postformTime", "timeOut", "postformProgress", "callback", "id", "onUploadProgress", "e", "filedownload", "responseType", "fileRequest", "text", "content", "blob", "Blob", "fileName", "document", "createElement", "elink", "download", "style", "display", "href", "URL", "createObjectURL", "body", "append<PERSON><PERSON><PERSON>", "click", "revokeObjectURL", "<PERSON><PERSON><PERSON><PERSON>", "navigator", "msSaveBlob", "exportFile", "myForm", "baseURLForm", "action", "setAttribute", "submit", "_post", "postText", "toFormData", "FormData", "i", "postForm", "_exportFile", "success", "upload", "export"], "sources": ["D:/zy/xm/pc/qdzx/product/src/api/http.js"], "sourcesContent": ["import Vue from 'vue'\r\nimport axios from 'axios'\r\nimport Qs from 'qs'\r\nimport router from '../router'\r\nimport {\r\n  Message\r\n} from 'element-ui'\r\nvar loginUc = 'http://test.dc.cszysoft.com:21429/server'\r\nvar baseURL = 'http://test.dc.cszysoft.com:20701/lzt'\r\nvar yunpan = 'http://212.64.102.79/chanpinstore'\r\nvar yiyangjy = 'http://118.25.54.81/YYSRDceshi'\r\nvar dataCenter = 'http://220.170.144.85:8081/yyrddc'\r\nif (process.env.STAGE == 'qdzx') { // eslint-disable-line\r\n  baseURL = 'http://test.dc.cszysoft.com:21408/lzt' // 青岛政协测试环境\r\n  // baseURL = 'https://qdzhzx.qingdao.gov.cn/lzt' // 青岛政协正式环境\r\n  // loginUc = 'https://qdzhzx.qingdao.gov.cn/server'\r\n  // baseURL = 'http://**************/lzt' // 青岛政协正式环境\r\n  // loginUc = 'http://**************/server'\r\n} else if (process.env.STAGE === 'zht') {\r\n  baseURL = 'https://www.hnzhihuitong.com/zht_qingdaord/a' // 智会通测试环境\r\n} else if (process.env.STAGE === 'prod') {\r\n  loginUc = `${window.location.protocol}//${window.location.host}/server`\r\n  baseURL = `${window.location.protocol}//${window.location.host}/lzt`\r\n}\r\nexport {\r\n  loginUc,\r\n  baseURL,\r\n  yunpan,\r\n  yiyangjy,\r\n  dataCenter\r\n}\r\naxios.defaults.baseURL = baseURL\r\n// 请求超时时间\r\naxios.defaults.timeout = 180000\r\n// 设置post请求头\r\naxios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'\r\n// 请求拦截器\r\naxios.interceptors.request.use(\r\n  config => {\r\n    if (/^\\/apis/.test(config.url)) {\r\n      config.baseURL = ''\r\n      return config\r\n    }\r\n    var switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''\r\n    if (switchpage) {\r\n      config.baseURL = switchpage\r\n    }\r\n    // 自定义请求头参数\r\n    var token = ''\r\n    if (sessionStorage.getItem('qdzxtoken')) {\r\n      token = ''\r\n    } else {\r\n      token = JSON.parse(sessionStorage.getItem('token' + Vue.prototype.$logo())) || ''\r\n    }\r\n    const scanningtoken = JSON.parse(sessionStorage.getItem('scanningtoken')) || ''\r\n    let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n    if (token) {\r\n      tokenid = token\r\n    } else if (scanningtoken) {\r\n      tokenid = 'bearer ' + scanningtoken\r\n    }\r\n    // config.headers.isOutSideNet = window.location.origin === 'http://test.dc.cszysoft.com:21408' ? 'test' : window.location.origin === 'http://qdzhzx.qingdao.gov.cn' ? true : window.location.origin === 'http://172.20.236.51:809' ? 2 : 'test'\r\n    const bigDataUrl = sessionStorage[`BigDataUrl${sessionStorage.theme}`] || ''\r\n    if (bigDataUrl) {\r\n      const bigDataUrlJson = JSON.parse(bigDataUrl)\r\n      if (config.url.indexOf(bigDataUrlJson) !== -1) {\r\n        config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''\r\n      }\r\n    }\r\n\r\n    var urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\\.?/\r\n    var url = urlReg.exec(config.url)\r\n    if (url) {\r\n      if (url[0] === '103.239.154.90') {\r\n        if (config.method === 'post') {\r\n          if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n            if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n              config.data = Qs.stringify(filter(config.data))\r\n            }\r\n          }\r\n        } else if (config.method === 'get') {\r\n          config.params = filter(config.params)\r\n        }\r\n        return config\r\n      }\r\n    }\r\n    // if (url && url[0] !== urlReg.exec(loginUc)[0]) {\r\n    //   const bigDataUrl = urlReg.exec(JSON.parse(sessionStorage[`BigDataUrl${sessionStorage.theme}`]))[0]\r\n    //   if (url[0] === bigDataUrl) {\r\n    //     config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''\r\n    //   }\r\n    //   if (url[0] === 'proposal.yiyang.gov.cn' || url[0] === '212.64.61.94') {\r\n    //     var Authorization = sessionStorage.getItem('Authorization') || ''\r\n    //     if (Authorization) {\r\n    //       Authorization = 'Bearer ' + JSON.parse(Authorization)\r\n    //       config.headers.Authorization = Authorization\r\n    //     }\r\n    //     if (config.method === 'post') {\r\n    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n    //           config.data = Qs.stringify(filter(config.data))\r\n    //         }\r\n    //       }\r\n    //     } else if (config.method === 'get') {\r\n    //       config.params = filter(config.params)\r\n    //     }\r\n    //     return config\r\n    //   } if (url[0] === '118.25.54.81' || url[0] === '103.239.154.90') {\r\n    //     if (config.method === 'post') {\r\n    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n    //           config.data = Qs.stringify(filter(config.data))\r\n    //         }\r\n    //       }\r\n    //     } else if (config.method === 'get') {\r\n    //       config.params = filter(config.params)\r\n    //     }\r\n    //     return config\r\n    //   }\r\n    // }\r\n    config.headers.isOutSideNet = window.location.hostname === 'qdzhzx.qingdao.gov.cn' || window.location.hostname === '**************' ? true : window.location.origin === 'http://*************' ? 'sdt' : 'test'\r\n    // config.headers.isOutSideNet = (`${window.location.protocol}//${window.location.host}` === 'http://qdzhzx.qingdao.gov.cn' || `${window.location.protocol}//${window.location.host}` === 'http://**************')\r\n    const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || '370200'\r\n    if (!!window.ActiveXObject || 'ActiveXObject' in window) { // 判断是不是ie浏览器\r\n      if (config.method === 'post') {\r\n        // 判断是不是formdata格式\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          config.data = Qs.stringify(filter({\r\n            loginToken: tokenid,\r\n            loginAreaId: areaId,\r\n            ...config.data\r\n          }))\r\n          if (sessionStorage.getItem('qdzxtoken')) {\r\n            if (config.url.indexOf('?') === -1) {\r\n              config.url += `?qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`\r\n            } else {\r\n              config.url += `&qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`\r\n            }\r\n          }\r\n        } else {\r\n          config.data.append('loginToken', tokenid)\r\n          config.data.append('loginAreaId', areaId)\r\n        }\r\n      } else if (config.method === 'get') {\r\n        config.params = filter({\r\n          ...config.params,\r\n          loginToken: tokenid,\r\n          loginAreaId: areaId\r\n        })\r\n        if (!config.params) {\r\n          config.params = Qs.stringify(filter({\r\n            loginToken: tokenid,\r\n            loginAreaId: areaId\r\n          }))\r\n        }\r\n      }\r\n      console.log(config)\r\n    } else {\r\n      config.headers.Authorization = tokenid\r\n      config.headers['u-login-areaId'] = areaId\r\n      if (sessionStorage.getItem('qdzxtoken')) {\r\n        config.headers.qdzxtoken = JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken\r\n      }\r\n      if (config.method === 'post') {\r\n        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line\r\n          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {\r\n            config.data = Qs.stringify(filter(config.data))\r\n          }\r\n        }\r\n      } else if (config.method === 'get') {\r\n        config.params = filter(config.params)\r\n      }\r\n    }\r\n    return config\r\n  }, error => {\r\n    // 对请求错误做些什么\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n// 响应拦截器\r\naxios.interceptors.response.use(\r\n  response => {\r\n    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据\r\n    // 否则的话抛出错误\r\n    var code = response.data.errcode || response.data.code\r\n    var message = response.data.errmsg || response.data.message\r\n    if (code === 200) {\r\n      return Promise.resolve(response)\r\n    } else if (code === 302) {\r\n      Message.error(message)\r\n      sessionStorage.clear()\r\n      router.push({\r\n        name: 'login'\r\n      })\r\n      return Promise.reject(response)\r\n    } else if (code === 400) {\r\n      // Message.error('参数异常')\r\n      Message.error(message)\r\n      return Promise.reject(response)\r\n    } else if (code === undefined) { // undefind 为文件下载接口\r\n      return Promise.resolve(response)\r\n    } else {\r\n      Message({\r\n        message: message || '请求异常',\r\n        type: 'error'\r\n      })\r\n      return Promise.reject(response)\r\n    }\r\n  }, error => {\r\n    if (error && error.message.includes('timeout')) {\r\n      Message.error('请求超时，请稍后重试！')\r\n      return Promise.reject(error)\r\n    }\r\n    console.log('error===>>', error)\r\n    // if (error && error.response.data.errmsg) {\r\n    //   Message.error(error.response.data.errmsg)\r\n    //   return Promise.reject(error)\r\n    // }\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\nfunction filter (param) { // 参数过滤\r\n  var data = param\r\n  for (var key in data) {\r\n    if (data[key] === null) {\r\n      delete data[key]\r\n    }\r\n  }\r\n  return data\r\n}\r\n// 封装get请求\r\nexport const get = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.get(url, {\r\n      headers: headers,\r\n      params: params\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\n// 封装post请求加了qs序列化数据\r\nexport const post = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, filter(params), {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postform = (url, params, headers) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postformTime = (url, params, timeOut) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, timeOut)\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 封装post请求用于FormData请求\r\nexport const postformProgress = (url, params, timeout, callback, id) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      url,\r\n      method: 'post',\r\n      data: params,\r\n      timeout,\r\n      onUploadProgress: e => {\r\n        callback(e, id)\r\n      }\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n/* 接收后台文件流 */\r\nexport const filedownload = (url, params, type = 'blob') => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      method: 'post',\r\n      url: url,\r\n      data: params,\r\n      responseType: type\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\n// 接收后台文件流\r\nexport const fileRequest = (url, params, text) => {\r\n  return axios({\r\n    method: 'post',\r\n    url: url,\r\n    data: params,\r\n    responseType: 'blob'\r\n  }).then(res => {\r\n    const content = res.data\r\n    const blob = new Blob([content])\r\n    const fileName = text\r\n    if ('download' in document.createElement('a')) { // 非IE下载\r\n      const elink = document.createElement('a')\r\n      elink.download = fileName\r\n      elink.style.display = 'none'\r\n      elink.href = URL.createObjectURL(blob)\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      URL.revokeObjectURL(elink.href) // 释放URL 对象\r\n      document.body.removeChild(elink)\r\n    } else { // IE10+下载\r\n      navigator.msSaveBlob(blob, fileName)\r\n    }\r\n  })\r\n}\r\n// 导出文件的方法\r\nexport const exportFile = (url, params) => {\r\n  var myForm = document.createElement('form')\r\n  myForm.method = 'post'\r\n  const switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''\r\n  var baseURLForm = baseURL\r\n  if (switchpage) {\r\n    baseURLForm = switchpage\r\n  }\r\n  myForm.action = `${baseURLForm}${url}`\r\n  document.body.appendChild(myForm)\r\n  const token = sessionStorage.getItem('token' + Vue.prototype.$logo()) || ''\r\n  let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'\r\n  if (token) {\r\n    tokenid = JSON.parse(token)\r\n  }\r\n  const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || ''\r\n  var loginToken = document.createElement('input')\r\n  loginToken.setAttribute('name', 'loginToken')\r\n  loginToken.setAttribute('value', tokenid)\r\n  myForm.appendChild(loginToken)\r\n  var loginAreaId = document.createElement('input')\r\n  loginAreaId.setAttribute('name', 'loginAreaId')\r\n  loginAreaId.setAttribute('value', areaId)\r\n  myForm.appendChild(loginAreaId)\r\n  for (const key in params) {\r\n    var name = 'input' + key\r\n    window[name] = document.createElement('input')\r\n    window[name].setAttribute('name', key)\r\n    window[name].setAttribute('value', params[key])\r\n    myForm.appendChild(window[name])\r\n  }\r\n  myForm.submit()\r\n  document.body.removeChild(myForm)\r\n}\r\n// json 参数形式\r\nexport const _post = (url, params) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: {\r\n        'Content-Type': 'application/json;charset=UTF-8'\r\n      }\r\n    }).then(res => {\r\n      resolve(res.data)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\nconst postText = (url, params) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, Qs.stringify(params), {\r\n      headers: {\r\n        'Content-Type': 'application/json;charset=UTF-8'\r\n      }\r\n    }).then(res => {\r\n      resolve(res)\r\n    }).catch(err => {\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\n\r\nfunction toFormData (params) {\r\n  const data = new FormData()\r\n  for (var i in params) {\r\n    data.append(i, params[i])\r\n  }\r\n  return data\r\n}\r\nconst postForm = (url, params, headers) => {\r\n  params = toFormData(params)\r\n  return new Promise((resolve, reject) => {\r\n    axios.post(url, params, {\r\n      headers: headers\r\n    })\r\n      .then(res => {\r\n        resolve(res.data)\r\n      })\r\n      .catch(err => {\r\n        reject(err)\r\n      })\r\n  })\r\n}\r\nconst _exportFile = (url, params, fileName) => {\r\n  return new Promise((resolve, reject) => {\r\n    axios({\r\n      method: 'post',\r\n      url: url,\r\n      data: params,\r\n      responseType: 'blob'\r\n    }).then(res => {\r\n      const blob = new Blob([res.request.response])\r\n      const elink = document.createElement('a')\r\n      elink.download = fileName\r\n      elink.style.display = 'none'\r\n      elink.href = URL.createObjectURL(blob)\r\n      document.body.appendChild(elink)\r\n      elink.click()\r\n      document.body.removeChild(elink)\r\n      Message.success('导出成功')\r\n      resolve(res)\r\n    }).catch(err => {\r\n      Message.error('导出失败')\r\n      reject(err)\r\n    })\r\n  })\r\n}\r\nexport default {\r\n  get,\r\n  post: _post,\r\n  postText,\r\n  postForm,\r\n  upload: postform,\r\n  export: _exportFile\r\n}\r\n"], "mappings": ";;;AAAA,OAAOA,GAAP,MAAgB,KAAhB;AACA,OAAOC,KAAP,MAAkB,OAAlB;AACA,OAAOC,EAAP,MAAe,IAAf;AACA,OAAOC,MAAP,MAAmB,WAAnB;AAIA,IAAIC,OAAO,GAAG,0CAAd;AACA,IAAIC,OAAO,GAAG,uCAAd;AACA,IAAIC,MAAM,GAAG,mCAAb;AACA,IAAIC,QAAQ,GAAG,gCAAf;AACA,IAAIC,UAAU,GAAG,mCAAjB;;AACA,IAAIC,OAAO,CAACC,GAAR,CAAYC,KAAZ,IAAqB,MAAzB,EAAiC;EAAE;EACjCN,OAAO,GAAG,uCAAV,CAD+B,CACmB;EAClD;EACA;EACA;EACA;AACD,CAND,MAMO,IAAII,OAAO,CAACC,GAAR,CAAYC,KAAZ,KAAsB,KAA1B,EAAiC;EACtCN,OAAO,GAAG,8CAAV,CADsC,CACmB;AAC1D,CAFM,MAEA,IAAII,OAAO,CAACC,GAAR,CAAYC,KAAZ,KAAsB,MAA1B,EAAkC;EACvCP,OAAO,GAAI,GAAEQ,MAAM,CAACC,QAAP,CAAgBC,QAAS,KAAIF,MAAM,CAACC,QAAP,CAAgBE,IAAK,SAA/D;EACAV,OAAO,GAAI,GAAEO,MAAM,CAACC,QAAP,CAAgBC,QAAS,KAAIF,MAAM,CAACC,QAAP,CAAgBE,IAAK,MAA/D;AACD;;AACD,SACEX,OADF,EAEEC,OAFF,EAGEC,MAHF,EAIEC,QAJF,EAKEC,UALF;AAOAP,KAAK,CAACe,QAAN,CAAeX,OAAf,GAAyBA,OAAzB,C,CACA;;AACAJ,KAAK,CAACe,QAAN,CAAeC,OAAf,GAAyB,MAAzB,C,CACA;;AACAhB,KAAK,CAACe,QAAN,CAAeE,OAAf,CAAuBC,IAAvB,CAA4B,cAA5B,IAA8C,iDAA9C,C,CACA;;AACAlB,KAAK,CAACmB,YAAN,CAAmBC,OAAnB,CAA2BC,GAA3B,CACEC,MAAM,IAAI;EACR,IAAI,UAAUC,IAAV,CAAeD,MAAM,CAACE,GAAtB,CAAJ,EAAgC;IAC9BF,MAAM,CAAClB,OAAP,GAAiB,EAAjB;IACA,OAAOkB,MAAP;EACD;;EACD,IAAIG,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,YAAvB,CAAX,KAAoD,EAArE;;EACA,IAAIJ,UAAJ,EAAgB;IACdH,MAAM,CAAClB,OAAP,GAAiBqB,UAAjB;EACD,CARO,CASR;;;EACA,IAAIK,KAAK,GAAG,EAAZ;;EACA,IAAIF,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;IACvCC,KAAK,GAAG,EAAR;EACD,CAFD,MAEO;IACLA,KAAK,GAAGJ,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,UAAU9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAjC,CAAX,KAAuE,EAA/E;EACD;;EACD,MAAMC,aAAa,GAAGP,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,eAAvB,CAAX,KAAuD,EAA7E;EACA,IAAIK,OAAO,GAAG,gCAAd;;EACA,IAAIJ,KAAJ,EAAW;IACTI,OAAO,GAAGJ,KAAV;EACD,CAFD,MAEO,IAAIG,aAAJ,EAAmB;IACxBC,OAAO,GAAG,YAAYD,aAAtB;EACD,CAtBO,CAuBR;;;EACA,MAAME,UAAU,GAAGP,cAAc,CAAE,aAAYA,cAAc,CAACQ,KAAM,EAAnC,CAAd,IAAuD,EAA1E;;EACA,IAAID,UAAJ,EAAgB;IACd,MAAME,cAAc,GAAGX,IAAI,CAACC,KAAL,CAAWQ,UAAX,CAAvB;;IACA,IAAIb,MAAM,CAACE,GAAP,CAAWc,OAAX,CAAmBD,cAAnB,MAAuC,CAAC,CAA5C,EAA+C;MAC7Cf,MAAM,CAACL,OAAP,CAAesB,YAAf,GAA8Bb,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,gBAAgB9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAvC,CAAX,KAA6E,EAA3G;IACD;EACF;;EAED,IAAIQ,MAAM,GAAG,oEAAb;EACA,IAAIhB,GAAG,GAAGgB,MAAM,CAACC,IAAP,CAAYnB,MAAM,CAACE,GAAnB,CAAV;;EACA,IAAIA,GAAJ,EAAS;IACP,IAAIA,GAAG,CAAC,CAAD,CAAH,KAAW,gBAAf,EAAiC;MAC/B,IAAIF,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;QAC5B,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;UAAE;UACxE,IAAIxB,MAAM,CAACL,OAAP,CAAe,cAAf,MAAmC,gCAAvC,EAAyE;YACvEK,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC1B,MAAM,CAACwB,IAAR,CAAnB,CAAd;UACD;QACF;MACF,CAND,MAMO,IAAIxB,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;QAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC1B,MAAM,CAAC2B,MAAR,CAAtB;MACD;;MACD,OAAO3B,MAAP;IACD;EACF,CA/CO,CAgDR;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;;EACAA,MAAM,CAACL,OAAP,CAAeiC,YAAf,GAA8BvC,MAAM,CAACC,QAAP,CAAgBuC,QAAhB,KAA6B,uBAA7B,IAAwDxC,MAAM,CAACC,QAAP,CAAgBuC,QAAhB,KAA6B,gBAArF,GAAwG,IAAxG,GAA+GxC,MAAM,CAACC,QAAP,CAAgBwC,MAAhB,KAA2B,sBAA3B,GAAoD,KAApD,GAA4D,MAAzM,CAlFQ,CAmFR;;EACA,MAAMC,MAAM,GAAG3B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAW9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAlC,CAAX,KAAwE,QAAvF;;EACA,IAAI,CAAC,CAACrB,MAAM,CAAC2C,aAAT,IAA0B,mBAAmB3C,MAAjD,EAAyD;IAAE;IACzD,IAAIW,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;MAC5B;MACA,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;QAAE;QACxExB,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC;UAChCO,UAAU,EAAErB,OADoB;UAEhCsB,WAAW,EAAEH,MAFmB;UAGhC,GAAG/B,MAAM,CAACwB;QAHsB,CAAD,CAAnB,CAAd;;QAKA,IAAIlB,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;UACvC,IAAIP,MAAM,CAACE,GAAP,CAAWc,OAAX,CAAmB,GAAnB,MAA4B,CAAC,CAAjC,EAAoC;YAClChB,MAAM,CAACE,GAAP,IAAe,cAAaiC,SAAS,CAAC/B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD6B,SAAjD,CAA4D,EAAjG;UACD,CAFD,MAEO;YACLpC,MAAM,CAACE,GAAP,IAAe,cAAaiC,SAAS,CAAC/B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD6B,SAAjD,CAA4D,EAAjG;UACD;QACF;MACF,CAbD,MAaO;QACLpC,MAAM,CAACwB,IAAP,CAAYa,MAAZ,CAAmB,YAAnB,EAAiCzB,OAAjC;QACAZ,MAAM,CAACwB,IAAP,CAAYa,MAAZ,CAAmB,aAAnB,EAAkCN,MAAlC;MACD;IACF,CAnBD,MAmBO,IAAI/B,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;MAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC,EACrB,GAAG1B,MAAM,CAAC2B,MADW;QAErBM,UAAU,EAAErB,OAFS;QAGrBsB,WAAW,EAAEH;MAHQ,CAAD,CAAtB;;MAKA,IAAI,CAAC/B,MAAM,CAAC2B,MAAZ,EAAoB;QAClB3B,MAAM,CAAC2B,MAAP,GAAgBhD,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC;UAClCO,UAAU,EAAErB,OADsB;UAElCsB,WAAW,EAAEH;QAFqB,CAAD,CAAnB,CAAhB;MAID;IACF;;IACDO,OAAO,CAACC,GAAR,CAAYvC,MAAZ;EACD,CAlCD,MAkCO;IACLA,MAAM,CAACL,OAAP,CAAe6C,aAAf,GAA+B5B,OAA/B;IACAZ,MAAM,CAACL,OAAP,CAAe,gBAAf,IAAmCoC,MAAnC;;IACA,IAAIzB,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAJ,EAAyC;MACvCP,MAAM,CAACL,OAAP,CAAeyC,SAAf,GAA2BhC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAvB,CAAX,EAAgD6B,SAA3E;IACD;;IACD,IAAIpC,MAAM,CAACoB,MAAP,KAAkB,MAAtB,EAA8B;MAC5B,IAAIC,MAAM,CAACZ,SAAP,CAAiBa,QAAjB,CAA0BC,IAA1B,CAA+BvB,MAAM,CAACwB,IAAtC,KAA+C,mBAAnD,EAAwE;QAAE;QACxE,IAAIxB,MAAM,CAACL,OAAP,CAAe,cAAf,MAAmC,gCAAvC,EAAyE;UACvEK,MAAM,CAACwB,IAAP,GAAc7C,EAAE,CAAC8C,SAAH,CAAaC,MAAM,CAAC1B,MAAM,CAACwB,IAAR,CAAnB,CAAd;QACD;MACF;IACF,CAND,MAMO,IAAIxB,MAAM,CAACoB,MAAP,KAAkB,KAAtB,EAA6B;MAClCpB,MAAM,CAAC2B,MAAP,GAAgBD,MAAM,CAAC1B,MAAM,CAAC2B,MAAR,CAAtB;IACD;EACF;;EACD,OAAO3B,MAAP;AACD,CAzIH,EAyIKyC,KAAK,IAAI;EACV;EACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACD,CA5IH,E,CA8IA;;AACA/D,KAAK,CAACmB,YAAN,CAAmB+C,QAAnB,CAA4B7C,GAA5B,CACE6C,QAAQ,IAAI;EACV;EACA;EACA,IAAIC,IAAI,GAAGD,QAAQ,CAACpB,IAAT,CAAcsB,OAAd,IAAyBF,QAAQ,CAACpB,IAAT,CAAcqB,IAAlD;EACA,IAAIE,OAAO,GAAGH,QAAQ,CAACpB,IAAT,CAAcwB,MAAd,IAAwBJ,QAAQ,CAACpB,IAAT,CAAcuB,OAApD;;EACA,IAAIF,IAAI,KAAK,GAAb,EAAkB;IAChB,OAAOH,OAAO,CAACO,OAAR,CAAgBL,QAAhB,CAAP;EACD,CAFD,MAEO,IAAIC,IAAI,KAAK,GAAb,EAAkB;IACvB,SAAQJ,KAAR,CAAcM,OAAd;;IACAzC,cAAc,CAAC4C,KAAf;IACAtE,MAAM,CAACuE,IAAP,CAAY;MACVC,IAAI,EAAE;IADI,CAAZ;IAGA,OAAOV,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD,CAPM,MAOA,IAAIC,IAAI,KAAK,GAAb,EAAkB;IACvB;IACA,SAAQJ,KAAR,CAAcM,OAAd;;IACA,OAAOL,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD,CAJM,MAIA,IAAIC,IAAI,KAAKQ,SAAb,EAAwB;IAAE;IAC/B,OAAOX,OAAO,CAACO,OAAR,CAAgBL,QAAhB,CAAP;EACD,CAFM,MAEA;IACL,SAAQ;MACNG,OAAO,EAAEA,OAAO,IAAI,MADd;MAENO,IAAI,EAAE;IAFA,CAAR;;IAIA,OAAOZ,OAAO,CAACC,MAAR,CAAeC,QAAf,CAAP;EACD;AACF,CA5BH,EA4BKH,KAAK,IAAI;EACV,IAAIA,KAAK,IAAIA,KAAK,CAACM,OAAN,CAAcQ,QAAd,CAAuB,SAAvB,CAAb,EAAgD;IAC9C,SAAQd,KAAR,CAAc,aAAd;;IACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;EACD;;EACDH,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BE,KAA1B,EALU,CAMV;EACA;EACA;EACA;;EACA,OAAOC,OAAO,CAACC,MAAR,CAAeF,KAAf,CAAP;AACD,CAvCH;;AA0CA,SAASf,MAAT,CAAiB8B,KAAjB,EAAwB;EAAE;EACxB,IAAIhC,IAAI,GAAGgC,KAAX;;EACA,KAAK,IAAIC,GAAT,IAAgBjC,IAAhB,EAAsB;IACpB,IAAIA,IAAI,CAACiC,GAAD,CAAJ,KAAc,IAAlB,EAAwB;MACtB,OAAOjC,IAAI,CAACiC,GAAD,CAAX;IACD;EACF;;EACD,OAAOjC,IAAP;AACD,C,CACD;;;AACA,OAAO,MAAMkC,GAAG,GAAG,CAACxD,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAC3C,OAAO,IAAI+C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACgF,GAAN,CAAUxD,GAAV,EAAe;MACbP,OAAO,EAAEA,OADI;MAEbgC,MAAM,EAAEA;IAFK,CAAf,EAGGgC,IAHH,CAGQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CALD,EAKGqC,KALH,CAKSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAPD;EAQD,CATM,CAAP;AAUD,CAXM,C,CAYP;;AACA,OAAO,MAAMlE,IAAI,GAAG,CAACM,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAC5C,OAAO,IAAI+C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgBwB,MAAM,CAACC,MAAD,CAAtB,EAAgC;MAC9BhC,OAAO,EAAEA;IADqB,CAAhC,EAGGgE,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CALH,EAMGqC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAZM,C,CAaP;;AACA,OAAO,MAAMC,QAAQ,GAAG,CAAC7D,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EAChD,OAAO,IAAI+C,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAEA;IADa,CAAxB,EAGGgE,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CALH,EAMGqC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAZM,C,CAaP;;AACA,OAAO,MAAME,YAAY,GAAG,CAAC9D,GAAD,EAAMyB,MAAN,EAAcsC,OAAd,KAA0B;EACpD,OAAO,IAAIvB,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwBsC,OAAxB,EACGN,IADH,CACQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CAHH,EAIGqC,KAJH,CAISC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CANH;EAOD,CARM,CAAP;AASD,CAVM,C,CAWP;;AACA,OAAO,MAAMI,gBAAgB,GAAG,CAAChE,GAAD,EAAMyB,MAAN,EAAcjC,OAAd,EAAuByE,QAAvB,EAAiCC,EAAjC,KAAwC;EACtE,OAAO,IAAI1B,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAAC;MACJwB,GADI;MAEJkB,MAAM,EAAE,MAFJ;MAGJI,IAAI,EAAEG,MAHF;MAIJjC,OAJI;MAKJ2E,gBAAgB,EAAEC,CAAC,IAAI;QACrBH,QAAQ,CAACG,CAAD,EAAIF,EAAJ,CAAR;MACD;IAPG,CAAD,CAAL,CASGT,IATH,CASQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CAXH,EAYGqC,KAZH,CAYSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAdH;EAeD,CAhBM,CAAP;AAiBD,CAlBM;AAmBP;;AACA,OAAO,MAAMS,YAAY,GAAG,CAACrE,GAAD,EAAMyB,MAAN,EAAc2B,IAAI,GAAG,MAArB,KAAgC;EAC1D,OAAO,IAAIZ,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAAC;MACJ0C,MAAM,EAAE,MADJ;MAEJlB,GAAG,EAAEA,GAFD;MAGJsB,IAAI,EAAEG,MAHF;MAIJ6C,YAAY,EAAElB;IAJV,CAAD,CAAL,CAMGK,IANH,CAMQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CARH,EASGqC,KATH,CASSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CAXH;EAYD,CAbM,CAAP;AAcD,CAfM,C,CAgBP;;AACA,OAAO,MAAMW,WAAW,GAAG,CAACvE,GAAD,EAAMyB,MAAN,EAAc+C,IAAd,KAAuB;EAChD,OAAOhG,KAAK,CAAC;IACX0C,MAAM,EAAE,MADG;IAEXlB,GAAG,EAAEA,GAFM;IAGXsB,IAAI,EAAEG,MAHK;IAIX6C,YAAY,EAAE;EAJH,CAAD,CAAL,CAKJb,IALI,CAKCC,GAAG,IAAI;IACb,MAAMe,OAAO,GAAGf,GAAG,CAACpC,IAApB;IACA,MAAMoD,IAAI,GAAG,IAAIC,IAAJ,CAAS,CAACF,OAAD,CAAT,CAAb;IACA,MAAMG,QAAQ,GAAGJ,IAAjB;;IACA,IAAI,cAAcK,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAlB,EAA+C;MAAE;MAC/C,MAAMC,KAAK,GAAGF,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAd;MACAC,KAAK,CAACC,QAAN,GAAiBJ,QAAjB;MACAG,KAAK,CAACE,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;MACAH,KAAK,CAACI,IAAN,GAAaC,GAAG,CAACC,eAAJ,CAAoBX,IAApB,CAAb;MACAG,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BR,KAA1B;MACAA,KAAK,CAACS,KAAN;MACAJ,GAAG,CAACK,eAAJ,CAAoBV,KAAK,CAACI,IAA1B,EAP6C,CAOb;;MAChCN,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BX,KAA1B;IACD,CATD,MASO;MAAE;MACPY,SAAS,CAACC,UAAV,CAAqBlB,IAArB,EAA2BE,QAA3B;IACD;EACF,CArBM,CAAP;AAsBD,CAvBM,C,CAwBP;;AACA,OAAO,MAAMiB,UAAU,GAAG,CAAC7F,GAAD,EAAMyB,MAAN,KAAiB;EACzC,IAAIqE,MAAM,GAAGjB,QAAQ,CAACC,aAAT,CAAuB,MAAvB,CAAb;EACAgB,MAAM,CAAC5E,MAAP,GAAgB,MAAhB;EACA,MAAMjB,UAAU,GAAGC,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,YAAvB,CAAX,KAAoD,EAAvE;EACA,IAAI0F,WAAW,GAAGnH,OAAlB;;EACA,IAAIqB,UAAJ,EAAgB;IACd8F,WAAW,GAAG9F,UAAd;EACD;;EACD6F,MAAM,CAACE,MAAP,GAAiB,GAAED,WAAY,GAAE/F,GAAI,EAArC;EACA6E,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BO,MAA1B;EACA,MAAMxF,KAAK,GAAGF,cAAc,CAACC,OAAf,CAAuB,UAAU9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAjC,KAA2D,EAAzE;EACA,IAAIE,OAAO,GAAG,gCAAd;;EACA,IAAIJ,KAAJ,EAAW;IACTI,OAAO,GAAGR,IAAI,CAACC,KAAL,CAAWG,KAAX,CAAV;EACD;;EACD,MAAMuB,MAAM,GAAG3B,IAAI,CAACC,KAAL,CAAWC,cAAc,CAACC,OAAf,CAAuB,WAAW9B,GAAG,CAACgC,SAAJ,CAAcC,KAAd,EAAlC,CAAX,KAAwE,EAAvF;EACA,IAAIuB,UAAU,GAAG8C,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAjB;EACA/C,UAAU,CAACkE,YAAX,CAAwB,MAAxB,EAAgC,YAAhC;EACAlE,UAAU,CAACkE,YAAX,CAAwB,OAAxB,EAAiCvF,OAAjC;EACAoF,MAAM,CAACP,WAAP,CAAmBxD,UAAnB;EACA,IAAIC,WAAW,GAAG6C,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAlB;EACA9C,WAAW,CAACiE,YAAZ,CAAyB,MAAzB,EAAiC,aAAjC;EACAjE,WAAW,CAACiE,YAAZ,CAAyB,OAAzB,EAAkCpE,MAAlC;EACAiE,MAAM,CAACP,WAAP,CAAmBvD,WAAnB;;EACA,KAAK,MAAMuB,GAAX,IAAkB9B,MAAlB,EAA0B;IACxB,IAAIyB,IAAI,GAAG,UAAUK,GAArB;IACApE,MAAM,CAAC+D,IAAD,CAAN,GAAe2B,QAAQ,CAACC,aAAT,CAAuB,OAAvB,CAAf;IACA3F,MAAM,CAAC+D,IAAD,CAAN,CAAa+C,YAAb,CAA0B,MAA1B,EAAkC1C,GAAlC;IACApE,MAAM,CAAC+D,IAAD,CAAN,CAAa+C,YAAb,CAA0B,OAA1B,EAAmCxE,MAAM,CAAC8B,GAAD,CAAzC;IACAuC,MAAM,CAACP,WAAP,CAAmBpG,MAAM,CAAC+D,IAAD,CAAzB;EACD;;EACD4C,MAAM,CAACI,MAAP;EACArB,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BI,MAA1B;AACD,CAjCM,C,CAkCP;;AACA,OAAO,MAAMK,KAAK,GAAG,CAACnG,GAAD,EAAMyB,MAAN,KAAiB;EACpC,OAAO,IAAIe,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAE;QACP,gBAAgB;MADT;IADa,CAAxB,EAIGgE,IAJH,CAIQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CAND,EAMGqC,KANH,CAMSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARD;EASD,CAVM,CAAP;AAWD,CAZM;;AAaP,MAAMwC,QAAQ,GAAG,CAACpG,GAAD,EAAMyB,MAAN,KAAiB;EAChC,OAAO,IAAIe,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgBvB,EAAE,CAAC8C,SAAH,CAAaE,MAAb,CAAhB,EAAsC;MACpChC,OAAO,EAAE;QACP,gBAAgB;MADT;IAD2B,CAAtC,EAIGgE,IAJH,CAIQC,GAAG,IAAI;MACbX,OAAO,CAACW,GAAD,CAAP;IACD,CAND,EAMGC,KANH,CAMSC,GAAG,IAAI;MACdnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARD;EASD,CAVM,CAAP;AAWD,CAZD;;AAcA,SAASyC,UAAT,CAAqB5E,MAArB,EAA6B;EAC3B,MAAMH,IAAI,GAAG,IAAIgF,QAAJ,EAAb;;EACA,KAAK,IAAIC,CAAT,IAAc9E,MAAd,EAAsB;IACpBH,IAAI,CAACa,MAAL,CAAYoE,CAAZ,EAAe9E,MAAM,CAAC8E,CAAD,CAArB;EACD;;EACD,OAAOjF,IAAP;AACD;;AACD,MAAMkF,QAAQ,GAAG,CAACxG,GAAD,EAAMyB,MAAN,EAAchC,OAAd,KAA0B;EACzCgC,MAAM,GAAG4E,UAAU,CAAC5E,MAAD,CAAnB;EACA,OAAO,IAAIe,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAACkB,IAAN,CAAWM,GAAX,EAAgByB,MAAhB,EAAwB;MACtBhC,OAAO,EAAEA;IADa,CAAxB,EAGGgE,IAHH,CAGQC,GAAG,IAAI;MACXX,OAAO,CAACW,GAAG,CAACpC,IAAL,CAAP;IACD,CALH,EAMGqC,KANH,CAMSC,GAAG,IAAI;MACZnB,MAAM,CAACmB,GAAD,CAAN;IACD,CARH;EASD,CAVM,CAAP;AAWD,CAbD;;AAcA,MAAM6C,WAAW,GAAG,CAACzG,GAAD,EAAMyB,MAAN,EAAcmD,QAAd,KAA2B;EAC7C,OAAO,IAAIpC,OAAJ,CAAY,CAACO,OAAD,EAAUN,MAAV,KAAqB;IACtCjE,KAAK,CAAC;MACJ0C,MAAM,EAAE,MADJ;MAEJlB,GAAG,EAAEA,GAFD;MAGJsB,IAAI,EAAEG,MAHF;MAIJ6C,YAAY,EAAE;IAJV,CAAD,CAAL,CAKGb,IALH,CAKQC,GAAG,IAAI;MACb,MAAMgB,IAAI,GAAG,IAAIC,IAAJ,CAAS,CAACjB,GAAG,CAAC9D,OAAJ,CAAY8C,QAAb,CAAT,CAAb;MACA,MAAMqC,KAAK,GAAGF,QAAQ,CAACC,aAAT,CAAuB,GAAvB,CAAd;MACAC,KAAK,CAACC,QAAN,GAAiBJ,QAAjB;MACAG,KAAK,CAACE,KAAN,CAAYC,OAAZ,GAAsB,MAAtB;MACAH,KAAK,CAACI,IAAN,GAAaC,GAAG,CAACC,eAAJ,CAAoBX,IAApB,CAAb;MACAG,QAAQ,CAACS,IAAT,CAAcC,WAAd,CAA0BR,KAA1B;MACAA,KAAK,CAACS,KAAN;MACAX,QAAQ,CAACS,IAAT,CAAcI,WAAd,CAA0BX,KAA1B;;MACA,SAAQ2B,OAAR,CAAgB,MAAhB;;MACA3D,OAAO,CAACW,GAAD,CAAP;IACD,CAhBD,EAgBGC,KAhBH,CAgBSC,GAAG,IAAI;MACd,SAAQrB,KAAR,CAAc,MAAd;;MACAE,MAAM,CAACmB,GAAD,CAAN;IACD,CAnBD;EAoBD,CArBM,CAAP;AAsBD,CAvBD;;AAwBA,eAAe;EACbJ,GADa;EAEb9D,IAAI,EAAEyG,KAFO;EAGbC,QAHa;EAIbI,QAJa;EAKbG,MAAM,EAAE9C,QALK;EAMb+C,MAAM,EAAEH;AANK,CAAf"}]}