{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js??ref--14-0!D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\module\\smartBrainLargeScreen.js", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\api\\module\\smartBrainLargeScreen.js", "mtime": 1758769279409}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\eslint-loader\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IHsgcG9zdCB9IGZyb20gJy4uL2h0dHAnOwpjb25zdCBzbWFydEJyYWluTGFyZ2VTY3JlZW4gPSB7CiAgLy8g5aeU5ZGY5YWa5rS+57uf6K6hCiAgbWVtYmVyUGFydHlTdGF0cyhwYXJhbXMpIHsKICAgIHJldHVybiBwb3N0KCd3aXNkb21TY3JlZW4vbWVtYmVyUGFydHlTdGF0cycsIHBhcmFtcyk7CiAgfSwKCiAgLy8g5bel5L2c5Yqo5oCB5YiX6KGoCiAgd29ya0R5bmFtaWNMaXN0KHBhcmFtcykgewogICAgcmV0dXJuIHBvc3QoJ3dpc2RvbVNjcmVlbi93b3JrRHluYW1pYy9saXN0JywgcGFyYW1zKTsKICB9LAoKICAvLyDlsaXogYznu5/orqEKICBkdXR5U3RhdGlzdGljcyhwYXJhbXMpIHsKICAgIHJldHVybiBwb3N0KCd3aXNkb21TY3JlZW4vZHV0eS9iaWdDYXRlZ29yeS9zdGF0cycsIHBhcmFtcyk7CiAgfSwKCiAgLy8g56S+5oOF5rCR5oSPCiAgc29jaWFsU3RhdGlzdGljcyhwYXJhbXMpIHsKICAgIHJldHVybiBwb3N0KCd3aXNkb21TY3JlZW4vc29jaWFsUmVwb3J0L3N0YXRzJywgcGFyYW1zKTsKICB9LAoKICAvLyDkvJrorq7mtLvliqgKICBtZWV0aW5nQWN0aXZpdHkocGFyYW1zKSB7CiAgICByZXR1cm4gcG9zdCgnd2lzZG9tU2NyZWVuL21lZXRpbmdBY3Rpdml0eVllYXJseScsIHBhcmFtcyk7CiAgfSwKCiAgLy8g572R57uc6K6u5pS/CiAgbmV0d29ya1BvbGl0aWNzKHBhcmFtcykgewogICAgcmV0dXJuIHBvc3QoJ3dpc2RvbVNjcmVlbi9zdXJ2ZXkvc3RhdHMnLCBwYXJhbXMpOwogIH0sCgogIC8vIOe9kee7nOiuruaUv+acgOeDreivnemimAogIGhvdFRvcGljc0xpc3QocGFyYW1zKSB7CiAgICByZXR1cm4gcG9zdCgnL3N1cnZleS9saXN0JywgcGFyYW1zKTsKICB9LAoKICAvLyDlsaXogYzmtLvliqjnsbvlnovnu5/orqEKICBkdXR5QW5hbHlzaXMocGFyYW1zKSB7CiAgICByZXR1cm4gcG9zdCgnL3dpc2RvbVNjcmVlbi9kdXR5L2FuYWx5c2lzJywgcGFyYW1zKTsKICB9LAoKICAvLyDlp5TlkZjnu5/orqHlpKflsY8KICBtZW1iZXJBbmFseXNpcyhwYXJhbXMpIHsKICAgIHJldHVybiBwb3N0KCcvd2lzZG9tU2NyZWVuL21lbWJlci9hbmFseXNpcycsIHBhcmFtcyk7CiAgfQoKfTsKZXhwb3J0IGRlZmF1bHQgc21hcnRCcmFpbkxhcmdlU2NyZWVuOw=="}, {"version": 3, "names": ["post", "smartBrainLargeScreen", "memberPartyStats", "params", "workDynamicList", "dutyStatistics", "socialStatistics", "meetingActivity", "networkPolitics", "hotTopicsList", "dutyAnalysis", "memberAnalysis"], "sources": ["D:/zy/xm/pc/qdzx/product/src/api/module/smartBrainLargeScreen.js"], "sourcesContent": ["import {\r\n  post\r\n} from '../http'\r\nconst smartBrainLargeScreen = {\r\n  // 委员党派统计\r\n  memberPartyStats (params) {\r\n    return post('wisdomScreen/memberPartyStats', params)\r\n  },\r\n  // 工作动态列表\r\n  workDynamicList (params) {\r\n    return post('wisdomScreen/workDynamic/list', params)\r\n  },\r\n  // 履职统计\r\n  dutyStatistics (params) {\r\n    return post('wisdomScreen/duty/bigCategory/stats', params)\r\n  },\r\n  // 社情民意\r\n  socialStatistics (params) {\r\n    return post('wisdomScreen/socialReport/stats', params)\r\n  },\r\n  // 会议活动\r\n  meetingActivity (params) {\r\n    return post('wisdomScreen/meetingActivityYearly', params)\r\n  },\r\n  // 网络议政\r\n  networkPolitics (params) {\r\n    return post('wisdomScreen/survey/stats', params)\r\n  },\r\n  // 网络议政最热话题\r\n  hotTopicsList (params) {\r\n    return post('/survey/list', params)\r\n  },\r\n  // 履职活动类型统计\r\n  dutyAnalysis (params) {\r\n    return post('/wisdomScreen/duty/analysis', params)\r\n  },\r\n  // 委员统计大屏\r\n  memberAnalysis (params) {\r\n    return post('/wisdomScreen/member/analysis', params)\r\n  }\r\n}\r\nexport default smartBrainLargeScreen\r\n"], "mappings": "AAAA,SACEA,IADF,QAEO,SAFP;AAGA,MAAMC,qBAAqB,GAAG;EAC5B;EACAC,gBAAgB,CAAEC,MAAF,EAAU;IACxB,OAAOH,IAAI,CAAC,+BAAD,EAAkCG,MAAlC,CAAX;EACD,CAJ2B;;EAK5B;EACAC,eAAe,CAAED,MAAF,EAAU;IACvB,OAAOH,IAAI,CAAC,+BAAD,EAAkCG,MAAlC,CAAX;EACD,CAR2B;;EAS5B;EACAE,cAAc,CAAEF,MAAF,EAAU;IACtB,OAAOH,IAAI,CAAC,qCAAD,EAAwCG,MAAxC,CAAX;EACD,CAZ2B;;EAa5B;EACAG,gBAAgB,CAAEH,MAAF,EAAU;IACxB,OAAOH,IAAI,CAAC,iCAAD,EAAoCG,MAApC,CAAX;EACD,CAhB2B;;EAiB5B;EACAI,eAAe,CAAEJ,MAAF,EAAU;IACvB,OAAOH,IAAI,CAAC,oCAAD,EAAuCG,MAAvC,CAAX;EACD,CApB2B;;EAqB5B;EACAK,eAAe,CAAEL,MAAF,EAAU;IACvB,OAAOH,IAAI,CAAC,2BAAD,EAA8BG,MAA9B,CAAX;EACD,CAxB2B;;EAyB5B;EACAM,aAAa,CAAEN,MAAF,EAAU;IACrB,OAAOH,IAAI,CAAC,cAAD,EAAiBG,MAAjB,CAAX;EACD,CA5B2B;;EA6B5B;EACAO,YAAY,CAAEP,MAAF,EAAU;IACpB,OAAOH,IAAI,CAAC,6BAAD,EAAgCG,MAAhC,CAAX;EACD,CAhC2B;;EAiC5B;EACAQ,cAAc,CAAER,MAAF,EAAU;IACtB,OAAOH,IAAI,CAAC,+BAAD,EAAkCG,MAAlC,CAAX;EACD;;AApC2B,CAA9B;AAsCA,eAAeF,qBAAf"}]}