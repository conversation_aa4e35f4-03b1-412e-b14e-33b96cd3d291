{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1758767185562}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}