import Vue from 'vue'
import axios from 'axios'
import Qs from 'qs'
import router from '../router'
import {
  Message
} from 'element-ui'
var loginUc = 'http://test.dc.cszysoft.com:21429/server'
var baseURL = 'http://test.dc.cszysoft.com:20701/lzt'
var yunpan = 'http://212.64.102.79/chanpinstore'
var yiyangjy = 'http://118.25.54.81/YYSRDceshi'
var dataCenter = 'http://220.170.144.85:8081/yyrddc'
if (process.env.STAGE == 'qdzx') { // eslint-disable-line
  baseURL = 'http://test.dc.cszysoft.com:21408/lzt' // 青岛政协测试环境
  // baseURL = 'https://qdzhzx.qingdao.gov.cn/lzt' // 青岛政协正式环境
  // loginUc = 'https://qdzhzx.qingdao.gov.cn/server'
  baseURL = 'http://**************/lzt' // 青岛政协正式环境
  loginUc = 'http://**************/server'
} else if (process.env.STAGE === 'zht') {
  baseURL = 'https://www.hnzhihuitong.com/zht_qingdaord/a' // 智会通测试环境
} else if (process.env.STAGE === 'prod') {
  loginUc = `${window.location.protocol}//${window.location.host}/server`
  baseURL = `${window.location.protocol}//${window.location.host}/lzt`
}
export {
  loginUc,
  baseURL,
  yunpan,
  yiyangjy,
  dataCenter
}
axios.defaults.baseURL = baseURL
// 请求超时时间
axios.defaults.timeout = 180000
// 设置post请求头
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded;charset=UTF-8'
// 请求拦截器
axios.interceptors.request.use(
  config => {
    if (/^\/apis/.test(config.url)) {
      config.baseURL = ''
      return config
    }
    var switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''
    if (switchpage) {
      config.baseURL = switchpage
    }
    // 自定义请求头参数
    var token = ''
    if (sessionStorage.getItem('qdzxtoken')) {
      token = ''
    } else {
      token = JSON.parse(sessionStorage.getItem('token' + Vue.prototype.$logo())) || ''
    }
    const scanningtoken = JSON.parse(sessionStorage.getItem('scanningtoken')) || ''
    let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'
    if (token) {
      tokenid = token
    } else if (scanningtoken) {
      tokenid = 'bearer ' + scanningtoken
    }
    // config.headers.isOutSideNet = window.location.origin === 'http://test.dc.cszysoft.com:21408' ? 'test' : window.location.origin === 'http://qdzhzx.qingdao.gov.cn' ? true : window.location.origin === 'http://172.20.236.51:809' ? 2 : 'test'
    const bigDataUrl = sessionStorage[`BigDataUrl${sessionStorage.theme}`] || ''
    if (bigDataUrl) {
      const bigDataUrlJson = JSON.parse(bigDataUrl)
      if (config.url.indexOf(bigDataUrlJson) !== -1) {
        config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''
      }
    }

    var urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/
    var url = urlReg.exec(config.url)
    if (url) {
      if (url[0] === '103.239.154.90') {
        if (config.method === 'post') {
          if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
            if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
              config.data = Qs.stringify(filter(config.data))
            }
          }
        } else if (config.method === 'get') {
          config.params = filter(config.params)
        }
        return config
      }
    }
    // if (url && url[0] !== urlReg.exec(loginUc)[0]) {
    //   const bigDataUrl = urlReg.exec(JSON.parse(sessionStorage[`BigDataUrl${sessionStorage.theme}`]))[0]
    //   if (url[0] === bigDataUrl) {
    //     config.headers.clientTypeId = JSON.parse(sessionStorage.getItem('BigDataUser' + Vue.prototype.$logo())) || ''
    //   }
    //   if (url[0] === 'proposal.yiyang.gov.cn' || url[0] === '212.64.61.94') {
    //     var Authorization = sessionStorage.getItem('Authorization') || ''
    //     if (Authorization) {
    //       Authorization = 'Bearer ' + JSON.parse(Authorization)
    //       config.headers.Authorization = Authorization
    //     }
    //     if (config.method === 'post') {
    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
    //           config.data = Qs.stringify(filter(config.data))
    //         }
    //       }
    //     } else if (config.method === 'get') {
    //       config.params = filter(config.params)
    //     }
    //     return config
    //   } if (url[0] === '118.25.54.81' || url[0] === '103.239.154.90') {
    //     if (config.method === 'post') {
    //       if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
    //         if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
    //           config.data = Qs.stringify(filter(config.data))
    //         }
    //       }
    //     } else if (config.method === 'get') {
    //       config.params = filter(config.params)
    //     }
    //     return config
    //   }
    // }
    config.headers.isOutSideNet = window.location.hostname === 'qdzhzx.qingdao.gov.cn' || window.location.hostname === '**************' ? true : window.location.origin === 'http://*************' ? 'sdt' : 'test'
    // config.headers.isOutSideNet = (`${window.location.protocol}//${window.location.host}` === 'http://qdzhzx.qingdao.gov.cn' || `${window.location.protocol}//${window.location.host}` === 'http://**************')
    const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || '370200'
    if (!!window.ActiveXObject || 'ActiveXObject' in window) { // 判断是不是ie浏览器
      if (config.method === 'post') {
        // 判断是不是formdata格式
        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
          config.data = Qs.stringify(filter({
            loginToken: tokenid,
            loginAreaId: areaId,
            ...config.data
          }))
          if (sessionStorage.getItem('qdzxtoken')) {
            if (config.url.indexOf('?') === -1) {
              config.url += `?qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`
            } else {
              config.url += `&qdzxtoken=${encodeURI(JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken)}`
            }
          }
        } else {
          config.data.append('loginToken', tokenid)
          config.data.append('loginAreaId', areaId)
        }
      } else if (config.method === 'get') {
        config.params = filter({
          ...config.params,
          loginToken: tokenid,
          loginAreaId: areaId
        })
        if (!config.params) {
          config.params = Qs.stringify(filter({
            loginToken: tokenid,
            loginAreaId: areaId
          }))
        }
      }
      console.log(config)
    } else {
      config.headers.Authorization = tokenid
      config.headers['u-login-areaId'] = areaId
      if (sessionStorage.getItem('qdzxtoken')) {
        config.headers.qdzxtoken = JSON.parse(sessionStorage.getItem('qdzxtoken')).qdzxtoken
      }
      if (config.method === 'post') {
        if (Object.prototype.toString.call(config.data) != '[object FormData]') { // eslint-disable-line
          if (config.headers['Content-Type'] !== 'application/json;charset=UTF-8') {
            config.data = Qs.stringify(filter(config.data))
          }
        }
      } else if (config.method === 'get') {
        config.params = filter(config.params)
      }
    }
    return config
  }, error => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)
// 响应拦截器
axios.interceptors.response.use(
  response => {
    // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
    // 否则的话抛出错误
    var code = response.data.errcode || response.data.code
    var message = response.data.errmsg || response.data.message
    if (code === 200) {
      return Promise.resolve(response)
    } else if (code === 302) {
      Message.error(message)
      sessionStorage.clear()
      router.push({
        name: 'login'
      })
      return Promise.reject(response)
    } else if (code === 400) {
      // Message.error('参数异常')
      Message.error(message)
      return Promise.reject(response)
    } else if (code === undefined) { // undefind 为文件下载接口
      return Promise.resolve(response)
    } else {
      Message({
        message: message || '请求异常',
        type: 'error'
      })
      return Promise.reject(response)
    }
  }, error => {
    if (error && error.message.includes('timeout')) {
      Message.error('请求超时，请稍后重试！')
      return Promise.reject(error)
    }
    console.log('error===>>', error)
    // if (error && error.response.data.errmsg) {
    //   Message.error(error.response.data.errmsg)
    //   return Promise.reject(error)
    // }
    return Promise.reject(error)
  }
)

function filter (param) { // 参数过滤
  var data = param
  for (var key in data) {
    if (data[key] === null) {
      delete data[key]
    }
  }
  return data
}
// 封装get请求
export const get = (url, params, headers) => {
  return new Promise((resolve, reject) => {
    axios.get(url, {
      headers: headers,
      params: params
    }).then(res => {
      resolve(res.data)
    }).catch(err => {
      reject(err)
    })
  })
}
// 封装post请求加了qs序列化数据
export const post = (url, params, headers) => {
  return new Promise((resolve, reject) => {
    axios.post(url, filter(params), {
      headers: headers
    })
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
// 封装post请求用于FormData请求
export const postform = (url, params, headers) => {
  return new Promise((resolve, reject) => {
    axios.post(url, params, {
      headers: headers
    })
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
// 封装post请求用于FormData请求
export const postformTime = (url, params, timeOut) => {
  return new Promise((resolve, reject) => {
    axios.post(url, params, timeOut)
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
// 封装post请求用于FormData请求
export const postformProgress = (url, params, timeout, callback, id) => {
  return new Promise((resolve, reject) => {
    axios({
      url,
      method: 'post',
      data: params,
      timeout,
      onUploadProgress: e => {
        callback(e, id)
      }
    })
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
/* 接收后台文件流 */
export const filedownload = (url, params, type = 'blob') => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: url,
      data: params,
      responseType: type
    })
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
// 接收后台文件流
export const fileRequest = (url, params, text) => {
  return axios({
    method: 'post',
    url: url,
    data: params,
    responseType: 'blob'
  }).then(res => {
    const content = res.data
    const blob = new Blob([content])
    const fileName = text
    if ('download' in document.createElement('a')) { // 非IE下载
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      URL.revokeObjectURL(elink.href) // 释放URL 对象
      document.body.removeChild(elink)
    } else { // IE10+下载
      navigator.msSaveBlob(blob, fileName)
    }
  })
}
// 导出文件的方法
export const exportFile = (url, params) => {
  var myForm = document.createElement('form')
  myForm.method = 'post'
  const switchpage = JSON.parse(sessionStorage.getItem('switchpage')) || ''
  var baseURLForm = baseURL
  if (switchpage) {
    baseURLForm = switchpage
  }
  myForm.action = `${baseURLForm}${url}`
  document.body.appendChild(myForm)
  const token = sessionStorage.getItem('token' + Vue.prototype.$logo()) || ''
  let tokenid = 'basic enlzb2Z0Onp5c29mdCo2MDc5'
  if (token) {
    tokenid = JSON.parse(token)
  }
  const areaId = JSON.parse(sessionStorage.getItem('areaId' + Vue.prototype.$logo())) || ''
  var loginToken = document.createElement('input')
  loginToken.setAttribute('name', 'loginToken')
  loginToken.setAttribute('value', tokenid)
  myForm.appendChild(loginToken)
  var loginAreaId = document.createElement('input')
  loginAreaId.setAttribute('name', 'loginAreaId')
  loginAreaId.setAttribute('value', areaId)
  myForm.appendChild(loginAreaId)
  for (const key in params) {
    var name = 'input' + key
    window[name] = document.createElement('input')
    window[name].setAttribute('name', key)
    window[name].setAttribute('value', params[key])
    myForm.appendChild(window[name])
  }
  myForm.submit()
  document.body.removeChild(myForm)
}
// json 参数形式
export const _post = (url, params) => {
  return new Promise((resolve, reject) => {
    axios.post(url, params, {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    }).then(res => {
      resolve(res.data)
    }).catch(err => {
      reject(err)
    })
  })
}
const postText = (url, params) => {
  return new Promise((resolve, reject) => {
    axios.post(url, Qs.stringify(params), {
      headers: {
        'Content-Type': 'application/json;charset=UTF-8'
      }
    }).then(res => {
      resolve(res)
    }).catch(err => {
      reject(err)
    })
  })
}

function toFormData (params) {
  const data = new FormData()
  for (var i in params) {
    data.append(i, params[i])
  }
  return data
}
const postForm = (url, params, headers) => {
  params = toFormData(params)
  return new Promise((resolve, reject) => {
    axios.post(url, params, {
      headers: headers
    })
      .then(res => {
        resolve(res.data)
      })
      .catch(err => {
        reject(err)
      })
  })
}
const _exportFile = (url, params, fileName) => {
  return new Promise((resolve, reject) => {
    axios({
      method: 'post',
      url: url,
      data: params,
      responseType: 'blob'
    }).then(res => {
      const blob = new Blob([res.request.response])
      const elink = document.createElement('a')
      elink.download = fileName
      elink.style.display = 'none'
      elink.href = URL.createObjectURL(blob)
      document.body.appendChild(elink)
      elink.click()
      document.body.removeChild(elink)
      Message.success('导出成功')
      resolve(res)
    }).catch(err => {
      Message.error('导出失败')
      reject(err)
    })
  })
}
export default {
  get,
  post: _post,
  postText,
  postForm,
  upload: postform,
  export: _exportFile
}
