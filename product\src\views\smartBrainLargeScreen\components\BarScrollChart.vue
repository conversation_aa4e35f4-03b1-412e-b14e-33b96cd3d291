<template>
  <div :id="id" class="bar-scroll-chart"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
  props: {
    id: {
      type: String,
      required: true
    },
    showCount: {
      type: Number,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    },
    alternateColors: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      chart: null,
      timer: null,
      currentIndex: 0
      // showCount: 5 // 一屏显示5条
    }
  },
  mounted () {
    this.initChart()
    this.startScroll()
  },
  beforeDestroy () {
    if (this.chart) this.chart.dispose()
    if (this.timer) clearInterval(this.timer)
  },
  methods: {
    getBarColor (index = 0) {
      if (this.alternateColors) {
        // 交替颜色：蓝色和黄色
        if (index % 2 === 0) {
          // 蓝色渐变
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#062553' },
              { offset: 1, color: 'rgba(31, 198, 255, 1)' }
            ]
          }
        } else {
          // 黄色渐变
          return {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            colorStops: [
              { offset: 0, color: '#072756' },
              { offset: 1, color: 'rgba(245, 231, 79, 1)' }
            ]
          }
        }
      } else {
        // 默认蓝色渐变
        return {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 1,
          y2: 0,
          colorStops: [
            { offset: 0, color: '#062553' },
            { offset: 1, color: 'rgba(31, 198, 255, 1)' }
          ]
        }
      }
    },
    getBgBarColor () {
      return 'rgba(35,225,255,0.08)'
    },
    getOption (data) {
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'none'
          },
          backgroundColor: 'rgba(0, 20, 40, 0.9)',
          borderColor: 'rgba(31, 198, 255, 0.8)',
          borderWidth: 1,
          textStyle: {
            color: '#FFFFFF',
            fontSize: 14
          },
          formatter: (params) => {
            if (params && params.length > 0) {
              const data = params[0]
              return `
                <div style="padding: 8px;">
                  <div style="color: #1FC6FF; font-weight: bold; margin-bottom: 4px;">
                    ${data.name}
                  </div>
                  <div style="color: #FFFFFF;">
                    数量: <span style="color: #F5E74F; font-weight: bold;">${data.value}</span>
                  </div>
                </div>
              `
            }
            return ''
          }
        },
        grid: {
          left: this.id === 'committee-statistics' ? 15 : this.id === 'activityTypeChart' ? 45 : this.id === 'sectorAnalysis' ? 45 : 35,
          right: 2,
          top: 15,
          bottom: 10,
          containLabel: true
        },
        xAxis: {
          type: 'value',
          min: 0,
          max: Math.max(...this.chartData.map(d => d.value)) * 1.1,
          splitLine: { show: false },
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: { show: false }
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            data: data.map(item => item.name),
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              align: 'right',
              margin: 16,
              formatter: (value, idx) => {
                if (this.id === 'activityTypeChart') {
                  return `{name|${value}}`
                } else {
                  const num = ((this.currentIndex + idx) % this.chartData.length) + 1
                  return `{num|${num.toString().padStart(2, '0')}}  {name|${value}}`
                }
              },
              rich: {
                num: {
                  color: 'rgba(255, 255, 255, 0.5)',
                  fontSize: 13,
                  fontFamily: 'DIN',
                  fontWeight: '500',
                  align: 'left',
                  // width: 25,
                  padding: [4, 0, 0, 0]
                },
                name: {
                  color: '#fff',
                  fontSize: 15,
                  padding: [0, 0, 0, 4]
                }
              }
            }
          },
          {
            // 右侧数值
            type: 'category',
            inverse: true,
            data: data.map(item => item.value),
            axisTick: { show: false },
            axisLine: { show: false },
            axisLabel: {
              show: true,
              color: '#A0F6FF',
              fontSize: 18,
              align: 'left',
              margin: 12
            }
          }
        ],
        series: [
          {
            type: 'bar',
            barWidth: 6,
            yAxisIndex: 0,
            data: data.map((item, index) => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: this.getBarColor(this.alternateColors ? (this.currentIndex + index) : 0),
                borderRadius: 6
              }
            })),
            z: 2
          },
          {
            // 背景条
            type: 'bar',
            barWidth: 8,
            yAxisIndex: 0,
            data: data.map(() => Math.max(...this.chartData.map(d => d.value)) * 1.1),
            itemStyle: {
              color: this.getBgBarColor(),
              borderRadius: 6
            },
            barGap: '-100%',
            z: 1
          }
        ]
      }
    },
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return
      this.chart = echarts.init(chartContainer)
      this.renderChart()
      window.addEventListener('resize', this.resizeChart)
    },
    renderChart () {
      // 滚动窗口数据
      let data = []
      if (this.chartData.length <= this.showCount) {
        data = this.chartData
      } else {
        // 实现无缝滚动
        const start = this.currentIndex
        const end = start + this.showCount
        if (end <= this.chartData.length) {
          data = this.chartData.slice(start, end)
        } else {
          data = this.chartData.slice(start).concat(this.chartData.slice(0, end - this.chartData.length))
        }
      }
      // 保持原顺序，新数据在最下方
      this.chart.setOption(this.getOption(data), true)
    },
    startScroll () {
      if (this.timer) clearInterval(this.timer)
      this.timer = setInterval(() => {
        if (this.chartData.length <= this.showCount) return
        this.currentIndex = (this.currentIndex + 1) % this.chartData.length
        this.renderChart()
      }, 3000)
    },
    resizeChart () {
      if (this.chart) this.chart.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.bar-scroll-chart {
  width: 100%;
  height: 100%;
}
</style>
