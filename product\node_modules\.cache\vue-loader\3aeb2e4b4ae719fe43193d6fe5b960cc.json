{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1758771826392}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["GenderRatioChart.vue"], "names": [], "mappings": ";AAoBA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "GenderRatioChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 0\n    },\n    femaleRatio: {\n      type: Number,\n      default: 0\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n      this.maleChart = echarts.init(chartContainer)\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(0, 212, 255, 0.1)',\n                borderWidth: 1\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n      this.femaleChart = echarts.init(chartContainer)\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(255, 215, 0, 0.1)',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n\n  .ratio-item {\n    position: relative;\n    width: 182px;\n    height: 172px;\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 100;\n      pointer-events: none;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}