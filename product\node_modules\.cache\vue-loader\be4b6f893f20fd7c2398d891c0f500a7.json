{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1758770115856}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PieChart.vue"], "names": [], "mappings": ";AAKA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    legendIcon: {\n      type: String,\n      default: '',\n      required: false\n    },\n    legendItemGap: {\n      type: Number,\n      default: 25,\n      required: false\n    },\n    legendItemWidth: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    legendItemHeight: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    radius: {\n      type: Array,\n      required: false,\n      default: () => ['55%', '80%']\n    },\n    center: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    lineRadius: {\n      type: Array,\n      required: false,\n      default: () => ['88%', '89%']\n    },\n    lineCenter: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    graphicLeft: {\n      type: String,\n      default: '17%',\n      required: false\n    },\n    graphicTop: {\n      type: String,\n      default: '26%',\n      required: false\n    },\n    graphicShapeR: {\n      type: Number,\n      default: 50,\n      required: false\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    legendShow: {\n      type: Boolean,\n      default: true,\n      required: false\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          show: this.legendShow,\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.legendItemWidth,\n          itemHeight: this.legendItemHeight,\n          icon: this.legendIcon,\n          itemGap: this.legendItemGap,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              // 计算总数\n              const total = this.chartData.reduce((sum, data) => sum + data.value, 0)\n              // 计算百分比\n              const percentage = item && total > 0 ? ((item.value / total) * 100).toFixed(2) : '0'\n              return `${name}  ${percentage}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.radius,\n            center: this.center,\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: () => {\n                if (!this.legendShow) {\n                  return `{value|${this.name}}\\n{label|采用率}`\n                }\n                return this.name\n              },\n              rich: {\n                value: {\n                  fontSize: 28,\n                  fontWeight: 'bold',\n                  color: '#FFFFFF',\n                  lineHeight: 35\n                },\n                label: {\n                  fontSize: 16,\n                  color: '#FFFFFF',\n                  lineHeight: 30\n                }\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: this.legendShow ? 3 : 0,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          ...(this.legendShow ? [{\n            type: 'pie',\n            radius: this.lineRadius,\n            center: this.lineCenter,\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }] : [])\n        ],\n        graphic: this.legendShow ? [\n          {\n            type: 'circle',\n            left: this.graphicLeft,\n            top: this.graphicTop,\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.graphicShapeR\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ] : []\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}