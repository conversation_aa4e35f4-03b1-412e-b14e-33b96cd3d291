import {
  post
} from '../http'
const smartBrainLargeScreen = {
  // 委员党派统计
  memberPartyStats (params) {
    return post('wisdomScreen/memberPartyStats', params)
  },
  // 工作动态列表
  workDynamicList (params) {
    return post('wisdomScreen/workDynamic/list', params)
  },
  // 履职统计
  dutyStatistics (params) {
    return post('wisdomScreen/duty/bigCategory/stats', params)
  },
  // 社情民意
  socialStatistics (params) {
    return post('wisdomScreen/socialReport/stats', params)
  },
  // 会议活动
  meetingActivity (params) {
    return post('wisdomScreen/meetingActivityYearly', params)
  },
  // 网络议政
  networkPolitics (params) {
    return post('wisdomScreen/survey/stats', params)
  },
  // 网络议政最热话题
  hotTopicsList (params) {
    return post('/survey/list', params)
  },
  // 履职活动类型统计
  dutyAnalysis (params) {
    return post('/wisdomScreen/duty/analysis', params)
  },
  // 委员统计大屏
  memberAnalysis (params) {
    return post('/wisdomScreen/member/analysis', params)
  }
}
export default smartBrainLargeScreen
