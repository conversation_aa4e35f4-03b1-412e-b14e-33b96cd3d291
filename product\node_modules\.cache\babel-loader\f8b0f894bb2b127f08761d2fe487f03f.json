{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue", "mtime": 1758774945802}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAUA;AACA;AAEA;EACAA,8BADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,WADA;MAEAC,cAFA;MAGAE;IAHA;EALA,CAFA;;EAaAC;IACA;MACAC,WADA;MAEAC,UAFA;MAGAC,kBAHA;MAIAC;IAJA;EAMA,CApBA;;EAqBAC;IACA;EACA,CAvBA;;EAwBAC;IACA;MACAC;IACA;;IACA;MACA;IACA;;IACAC;EACA,CAhCA;;EAiCAC;IACAX;MACAY;QACA;MACA,CAHA;;MAIAC;IAJA;EADA,CAjCA;EAyCAC;IACA;IACAC;MACA;MACA;MAEA,0CAJA,CAMA;;MACA,2CAPA,CASA;;MACA,8CAVA,CAYA;;MACA,kCAbA,CAeA;;MACA;MAEAL;IACA,CArBA;;IAuBA;IACAM;MACA;QACA,QACA;UACArB,aADA;UAEAsB,SAFA;UAGAC;YACAC;UADA;QAHA,CADA,EAQA;UACAxB,UADA;UAEAsB,SAFA;UAGAC;YACAC;UADA;QAHA,CARA,EAeA;UACAxB,UADA;UAEAsB,SAFA;UAGAC;YACAC;UADA;QAHA,CAfA;MAuBA,CAzBA,CA2BA;;;MACA,gBACA,0BADA,EACA,yBADA,EACA,yBADA,EAEA,0BAFA,EAEA,0BAFA,EAEA,0BAFA,EAGA,0BAHA,EAGA,0BAHA,EAGA,0BAHA,EAIA,0BAJA;MAOA;QACAxB,eADA;QAEAsB,iBAFA;QAGAC;UACAC;QADA;MAHA;IAOA,CAlEA;;IAmEA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA,CAFA,EATA,CAaA;;MACAA;QACAC;QACAA;MACA,CAHA,EAdA,CAmBA;;MACAC,6EApBA,CAqBA;;MACA;QACAC;QACA;UACA;UACA7B,6EAFA;UAGAG,eAHA;UAIA;UACA2B,gBALA;UAMA;UACAC;YACAC;UADA,CAPA;UAUAN,mBAVA;UAWAO;YACAC,eADA;YAEAC,cAFA;YAGAC;UAHA,CAXA;UAgBA;UACAC,uBAjBA;UAkBAC;QAlBA,EAFA,CAuBA;;QACA;UACA;;UACA;YACAf;UACA;;UACA;YACAA;UACA;;UACAgB;QACA;;QACAC;MACA,CAzDA,CA2DA;MACA;;;MACAC;MACAC;;MACA;QACAC;QACAH;QACAA;QACAA,oHACA,KADA,EACA,KADA,EACAJ,CADA,EACAI,uBADA;QAEAI;QACA;QACAH;UACAzC,oBADA;UAEAsB;QAFA;QAIAoB;UACA1C,oBADA;UAEAsB;QAFA;MAIA,CA/EA,CAiFA;;;MACA,+CAlFA,CAkFA;MACA;;MACA;QACA;QACAuB;UACAtC,gBADA;UAEA;UACAuC,kBAHA;UAIAC,WAJA;UAIA;UACAC,aALA;UAKA;UACAC,aANA;UAOAC,cAPA;UAQAC,WARA;UAQA;UACAC;YACA5B,gBADA;YAEA6B;UAFA,CATA;UAaA;UACAC;YACA;YACA;YACA;UACA;QAlBA,CAFA;QAsBA;QACAC;UACAD;YACA;cACA,2HACA,GADA,EACAE,OADA,CACA,CADA;cAEA,qCACA,yIADA,GAEA,SAFA;YAGA;UACA;QATA,CAvBA;QAkCA;QACAC;UACAC,OADA;UAEAC;QAFA,CAnCA;QAuCAC;UACAF,OADA;UAEAC;QAFA,CAvCA;QA2CAE;UACAH,OADA;UAEAC;QAFA,CA3CA;QA+CA;QACAG;UACA9B,WADA;UAEA+B,oBAFA;UAEA;UACA;UACAf,UAJA;UAIA;UACAgB,WALA;UAKA;UACAC,YANA;UAMA;UACA;UACAC;YACAC,aADA,CACA;;UADA,CARA;UAWAC;YACAC;cACAC,cADA;cAEAC,aAFA,CAEA;;YAFA,CADA;YAKAC;cACAF;YADA;UALA,CAXA;UAoBAG;YAAA;YACAC,SADA;YACA;YACAC,OAFA;YAEA;YACAC,aAHA;YAGA;YACAvC,iBAJA;YAIA;YACAwC,oBALA;YAKA;YACAC,kBANA;YAMA;YACAC,iBAPA;YAOA;YACAC,iBARA;YAQA;YACAC,gBATA,CASA;;UATA;QApBA,CAhDA;QAgFAzC,SACA,SADA,CAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QApEA;MAhFA;MAuJA;IACA,CAhTA;;IAiTA;IACA0C;MACA1C;QACA;MACA,CAFA;MAGA;IACA,CAvTA;;IAyTA;IACA2C;MACA;MACA;MACA;MACA;MACA,yCALA,CAMA;;MACA;QACAC;MACA,CATA,CAUA;;;MACAhD,yCAXA,CAYA;;MACA;MACA,2DAdA,CAeA;;MACA,uCAhBA,CAiBA;;MACA;QACAiD;UACA3B,aADA;UAEAC,gBAFA;UAGA2B;QAHA,CADA;QAMAC;UACA7B,MADA;UAEAC,gBAFA;UAGA2B;QAHA,CANA;QAWAE;UACA;YACA;UACA;;UACA;YACA;UACA;;UACA;QACA,CAnBA;QAoBAC;UACA;YACA;UACA;;UACA;YACA;UACA;;UACA;QACA,CA5BA;QA6BAC;UACA;YACA;UACA;;UACA;YACA;UACA;;UACA;QACA;MArCA;IAuCA,CAnXA;;IAqXA;IACAC;MACA;MACA;QACA;UACA7E;UACA8E;QACA,CAHA;;QAIA9E;QACA+E;MACA,CAPA;IAQA,CAhYA;;IAkYA;IACAC;MACA;MACA,wBAFA,CAIA;;MACAC;QACA;UACA,2CADA,CACA;QACA;;QACA;MACA,CALA;IAMA,CA9YA;;IAgZA;IACAC;MACA;MACA;MACA,sBAHA,CAIA;;MACAC;QACA;QACA;QACA;QACA;QACA;QACA,yEANA,CAOA;;QACA;UACAC,kFACAA,oDADA,EAEAA,kDAFA,EAGA,KAHA,EAIA,KAJA,EAKA9D,CALA,EAMA8D,+CANA;UAQAA;QACA,CAlBA,CAmBA;;;QACAA,6GACAd,UADA,EAEAe,SAFA,EAEA/D,CAFA,EAEA8D,oDAFA;QAGAA,uEAvBA,CAwBA;;QACA;UACAE;QACA,CA3BA,CA4BA;;;QACAF;MACA,CA9BA,EALA,CAqCA;;MACA;QACAA;UACAG;QACA,CAFA;MAGA,CAJA,EAIA,EAJA,EAtCA,CA0CA;MAEA;;MACAJ,4CA7CA,CA+CA;;MACAA;QACA;UACA;UACA;UACA;UACA;UACA;UAEAC,iFACAI,UADA,EACAC,QADA,EACAnB,UADA,EACAe,SADA,EACA/D,CADA,EAEA8D,8CAFA;UAIAA;UACAG,kBAZA,CAcA;;UACAH;QACA;MACA,CAlBA;IAmBA,CApdA;;IAsdA;IACAM;MACA;MACA;QACA,OADA,CACA;MACA,CAJA,CAMA;;;MACA,mDAPA,CASA;;MACA;QACApB;QACAe;QACAG;QACAC;QACAnE;QAEA,iFACAkE,UADA,EACAC,QADA,EACAnB,UADA,EACAe,SADA,EACA/D,CADA,EAEA,8CAFA;QAIA;MACA,CAtBA,CAwBA;;;MACA;QACAgD;QACAe;QACAG;QACAC;QACAnE;QAEA,uFACAkE,UADA,EACAC,QADA,EACAnB,UADA,EACAe,SADA,EACA/D,CADA,EAEA,wDAFA;QAIA;QACAqE;MACA,CAtCA,CAwCA;;;MACA;IACA,CAjgBA;;IAmgBA;IACAC;MACA;;MACA;QACA;MACA;;MACAC,wDALA,CAKA;;MACA;MACA,wBAPA,CAQA;;MACA;QACAC;QACAC;MACA;;MACA;QACAA;MACA;;MACA;IACA,CArhBA;;IAuhBAC;MACA;QACA;MACA;IACA;;EA3hBA;AAzCA", "names": ["name", "props", "id", "type", "required", "chartData", "default", "data", "chart", "option", "isAnimating", "animationTimer", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout", "window", "watch", "handler", "deep", "methods", "initChart", "convertChartData", "value", "itemStyle", "color", "getPie3D", "pieData", "item", "console", "sumValue", "parametric", "wireframe", "show", "pieStatus", "selected", "hovered", "k", "center", "radius", "seriesItem", "series", "legendData", "legendBfb", "endValue", "startValue", "legend", "orient", "right", "top", "itemWidth", "itemHeight", "itemGap", "textStyle", "fontSize", "formatter", "tooltip", "toFixed", "xAxis3D", "min", "max", "yAxis3D", "zAxis3D", "grid3D", "boxHeight", "left", "width", "postEffect", "enable", "light", "main", "intensity", "shadow", "ambient", "viewControl", "alpha", "beta", "distance", "rotateSensitivity", "zoomSensitivity", "panSensitivity", "autoRotate", "animation", "getHeight3D", "getParametricEquation", "isSelected", "u", "step", "v", "x", "y", "z", "debounce", "func", "timeout", "updateChartOption", "requestAnimationFrame", "bindListen", "myChart", "that", "isHovered", "selectedIndex", "hoveredIndex", "startRatio", "endRatio", "handleMouseover", "setHoveredIndex", "fomatFloat", "f", "rs", "s", "resizeChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart3D.vue"], "sourcesContent": ["<template>\n  <div class=\"party-distribution-chart\">\n    <!-- 3D饼图容器 -->\n    <div class=\"container\">\n      <div class=\"chartsGl\" :id=\"id\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'PartyDistributionChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      option: {},\n      isAnimating: false,\n      animationTimer: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.animationTimer) {\n      clearTimeout(this.animationTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 初始化构建3D饼图\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n\n      this.chart = echarts.init(chartContainer)\n\n      // 转换数据格式\n      const optionData = this.convertChartData()\n\n      // 传入数据生成 option ; getPie3D(数据，透明的空心占比（调节中间空心范围的0就是普通饼1就很镂空）)\n      this.option = this.getPie3D(optionData, 0.85)\n\n      // 将配置项设置进去\n      this.chart.setOption(this.option)\n\n      // 鼠标移动上去特效效果\n      this.bindListen(this.chart)\n\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    // 转换数据格式\n    convertChartData () {\n      if (!this.chartData || this.chartData.length === 0) {\n        return [\n          {\n            name: '中国共产党',\n            value: 32,\n            itemStyle: {\n              color: 'rgba(255, 107, 107, 0.8)'\n            }\n          },\n          {\n            name: '民革',\n            value: 15,\n            itemStyle: {\n              color: 'rgba(78, 205, 196, 0.8)'\n            }\n          },\n          {\n            name: '民盟',\n            value: 14,\n            itemStyle: {\n              color: 'rgba(69, 183, 209, 0.8)'\n            }\n          }\n        ]\n      }\n\n      // 预定义颜色数组\n      const colors = [\n        'rgba(255, 107, 107, 0.8)', 'rgba(78, 205, 196, 0.8)', 'rgba(69, 183, 209, 0.8)',\n        'rgba(150, 206, 180, 0.8)', 'rgba(255, 234, 167, 0.8)', 'rgba(221, 160, 221, 0.8)',\n        'rgba(152, 216, 200, 0.8)', 'rgba(247, 220, 111, 0.8)', 'rgba(187, 143, 206, 0.8)',\n        'rgba(133, 193, 233, 0.8)'\n      ]\n\n      return this.chartData.map((item, index) => ({\n        name: item.name,\n        value: item.value,\n        itemStyle: {\n          color: colors[index % colors.length]\n        }\n      }))\n    },\n    // 配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比\n    getPie3D (pieData, internalDiameterRatio) {\n      const that = this\n      const series = []\n      let sumValue = 0\n      let startValue = 0\n      let endValue = 0\n      let legendData = []\n      let legendBfb = []\n      const k = 1 - internalDiameterRatio\n      pieData.sort((a, b) => {\n        return (b.value - a.value)\n      })\n\n      // 标记前三名，用于显示线条指示\n      pieData.forEach((item, index) => {\n        item.isTopThree = index < 3\n        item.rank = index + 1\n      })\n\n      // 调试输出数据顺序\n      console.log('3D饼图数据顺序:', pieData.map(item => `${item.name}: ${item.value}`))\n      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置\n      for (let i = 0; i < pieData.length; i++) {\n        sumValue += pieData[i].value\n        const seriesItem = {\n          // 系统名称\n          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,\n          type: 'surface',\n          // 是否为参数曲面（是）\n          parametric: true,\n          // 曲面图网格线（否）上面一根一根的\n          wireframe: {\n            show: false\n          },\n          pieData: pieData[i],\n          pieStatus: {\n            selected: false,\n            hovered: false,\n            k: k\n          },\n          // 设置饼图在容器中的位置(目前没发现啥用)\n          center: ['80%', '100%'],\n          radius: '60%'\n        }\n\n        // 曲面的颜色、不透明度等样式。\n        if (typeof pieData[i].itemStyle !== 'undefined') {\n          const itemStyle = {}\n          if (typeof pieData[i].itemStyle.color !== 'undefined') {\n            itemStyle.color = pieData[i].itemStyle.color\n          }\n          if (typeof pieData[i].itemStyle.opacity !== 'undefined') {\n            itemStyle.opacity = pieData[i].itemStyle.opacity\n          }\n          seriesItem.itemStyle = itemStyle\n        }\n        series.push(seriesItem)\n      }\n\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\n      legendData = []\n      legendBfb = []\n      for (let i = 0; i < series.length; i++) {\n        endValue = startValue + series[i].pieData.value\n        series[i].pieData.startRatio = startValue / sumValue\n        series[i].pieData.endRatio = endValue / sumValue\n        series[i].parametricEquation = that.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,\n          false, false, k, series[i].pieData.value)\n        startValue = endValue\n        const bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4)\n        legendData.push({\n          name: series[i].name,\n          value: bfb\n        })\n        legendBfb.push({\n          name: series[i].name,\n          value: bfb\n        })\n      }\n\n      // (第二个参数可以设置你这个环形的高低程度)\n      const boxHeight = this.getHeight3D(series, 20) // 通过传参设定3d饼/环的高度\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\n      const option = {\n        // 图例组件\n        legend: {\n          data: legendData,\n          // 图例列表的布局朝向 - 垂直布局\n          orient: 'vertical',\n          right: '5%', // 距离右边5%\n          top: 'center', // 垂直居中\n          itemWidth: 14,\n          itemHeight: 14,\n          itemGap: 30, // 增加间距\n          textStyle: {\n            color: '#A1E2FF',\n            fontSize: 13\n          },\n          // 简化格式化，只显示名称和百分比\n          formatter: function (param) {\n            const item = legendBfb.filter(item => item.name === param)[0]\n            const bfs = that.fomatFloat(item.value * 100, 1) + '%'\n            return `${item.name}: ${bfs}`\n          }\n        },\n        // 移动上去提示的文本内容\n        tooltip: {\n          formatter: params => {\n            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n              const bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) *\n                100).toFixed(2)\n              return `${params.seriesName}<br/>` +\n                `<span style=\"display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>` +\n                `${bfb}%`\n            }\n          }\n        },\n        // 这个可以变形\n        xAxis3D: {\n          min: -1,\n          max: 1\n        },\n        yAxis3D: {\n          min: -1,\n          max: 1\n        },\n        zAxis3D: {\n          min: -1,\n          max: 1\n        },\n        // 此处是修改样式的重点\n        grid3D: {\n          show: false,\n          boxHeight: boxHeight, // 圆环的高度\n          // 这是饼图的位置 - 调整到合适位置\n          top: '-5%', // 适中的垂直位置\n          left: '-8%', // 适中的水平位置\n          width: '60%', // 限制图表宽度，为右侧图例留出空间\n          // 优化性能配置\n          postEffect: {\n            enable: false // 关闭后处理效果，提升性能\n          },\n          light: {\n            main: {\n              intensity: 1.2,\n              shadow: false // 关闭阴影，提升性能\n            },\n            ambient: {\n              intensity: 0.3\n            }\n          },\n          viewControl: { // 3d效果可以放大、旋转等，请自己去查看官方配置\n            alpha: 25, // 适中的俯视角度\n            beta: 0, // 水平旋转角度\n            distance: 150, // 适中的观察距离\n            center: [0, 0, 0], // 3D场景中心点\n            rotateSensitivity: 0, // 设置为0无法旋转\n            zoomSensitivity: 0, // 设置为0无法缩放\n            panSensitivity: 0, // 设置为0无法平移\n            autoRotate: false, // 自动旋转\n            animation: false // 关闭动画，提升性能\n          }\n        },\n        series: [\n          ...series\n          // 添加前三名的线条指示\n          // {\n          //   type: 'pie',\n          //   radius: ['30%', '60%'], // 调整半径以匹配3D饼图的视觉范围\n          //   center: ['30%', '45%'], // 微调中心点以更精确匹配3D饼图\n          //   startAngle: 30, // 进一步调整起始角度\n          //   clockwise: false, // 顺时针方向\n          //   // 使用完整数据以确保角度对应正确，但只为前三名设置标签\n          //   data: pieData.map(item => ({\n          //     name: item.name,\n          //     value: item.value,\n          //     itemStyle: {\n          //       color: 'transparent' // 透明，只显示标签线\n          //     },\n          //     label: {\n          //       show: item.isTopThree, // 只有前三名显示标签\n          //       position: 'outside',\n          //       fontSize: 12,\n          //       color: '#FFFFFF',\n          //       fontWeight: 'bold',\n          //       backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //       borderColor: '#A1E2FF',\n          //       borderWidth: 1,\n          //       borderRadius: 4,\n          //       padding: [3, 6],\n          //       formatter: `${item.name} ${item.value}人`\n          //     },\n          //     labelLine: {\n          //       show: item.isTopThree, // 只有前三名显示线条\n          //       length: 25, // 适中的第一段线条长度\n          //       length2: 15, // 适中的第二段线条长度\n          //       lineStyle: {\n          //         color: '#A1E2FF',\n          //         width: 2,\n          //         type: 'solid'\n          //       }\n          //     }\n          //   })),\n          //   // 全局标签配置（会被数据项中的配置覆盖）\n          //   label: {\n          //     show: false, // 默认不显示\n          //     position: 'outside',\n          //     fontSize: 12,\n          //     color: '#FFFFFF',\n          //     fontWeight: 'bold',\n          //     backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //     borderColor: '#A1E2FF',\n          //     borderWidth: 1,\n          //     borderRadius: 4,\n          //     padding: [3, 6],\n          //     formatter: function (params) {\n          //       return `${params.name} ${params.value}人`\n          //     }\n          //   },\n          //   labelLine: {\n          //     show: false, // 默认不显示\n          //     length: 25,\n          //     length2: 15,\n          //     lineStyle: {\n          //       color: '#A1E2FF',\n          //       width: 2,\n          //       type: 'solid'\n          //     }\n          //   },\n          //   silent: true,\n          //   z: 10\n          // }\n        ]\n      }\n      return option\n    },\n    // 获取3d饼图的最高扇区的高度\n    getHeight3D (series, height) {\n      series.sort((a, b) => {\n        return (b.pieData.value - a.pieData.value)\n      })\n      return height * 25 / series[0].pieData.value\n    },\n\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\n    getParametricEquation (startRatio, endRatio, isSelected, isHovered, k, h) {\n      // 计算\n      const midRatio = (startRatio + endRatio) / 2\n      const startRadian = startRatio * Math.PI * 2\n      const endRadian = endRatio * Math.PI * 2\n      const midRadian = midRatio * Math.PI * 2\n      // 如果只有一个扇形，则不实现选中效果。\n      if (startRatio === 0 && endRatio === 1) {\n        isSelected = false\n      }\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\n      k = typeof k !== 'undefined' ? k : 1 / 3\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\n      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0\n      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\n      const hoverRate = isHovered ? 1.05 : 1\n      // 返回曲面参数方程\n      return {\n        u: {\n          min: -Math.PI,\n          max: Math.PI * 3,\n          step: Math.PI / 16\n        },\n        v: {\n          min: 0,\n          max: Math.PI * 2,\n          step: Math.PI / 10\n        },\n        x: function (u, v) {\n          if (u < startRadian) {\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        y: function (u, v) {\n          if (u < startRadian) {\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        z: function (u, v) {\n          if (u < -Math.PI * 0.5) {\n            return Math.sin(u)\n          }\n          if (u > Math.PI * 2.5) {\n            return Math.sin(u) * h * 0.1\n          }\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1\n        }\n      }\n    },\n\n    // 防抖函数\n    debounce (func, wait) {\n      let timeout\n      return function executedFunction (...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 优化的更新图表方法\n    updateChartOption (option) {\n      if (this.isAnimating) return\n      this.isAnimating = true\n\n      // 使用requestAnimationFrame优化渲染\n      requestAnimationFrame(() => {\n        if (this.chart) {\n          this.chart.setOption(option, false, false) // 关闭动画和合并\n        }\n        this.isAnimating = false\n      })\n    },\n\n    // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。\n    bindListen (myChart) {\n      const that = this\n      let selectedIndex = ''\n      let hoveredIndex = ''\n      // 监听点击事件，实现选中效果（单选）\n      myChart.on('click', function (params) {\n        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。\n        const isSelected = !that.option.series[params.seriesIndex].pieStatus.selected\n        const isHovered = that.option.series[params.seriesIndex].pieStatus.hovered\n        const k = that.option.series[params.seriesIndex].pieStatus.k\n        const startRatio = that.option.series[params.seriesIndex].pieData.startRatio\n        const endRatio = that.option.series[params.seriesIndex].pieData.endRatio\n        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）\n        if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {\n          that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(\n            that.option.series[selectedIndex].pieData.startRatio,\n            that.option.series[selectedIndex].pieData.endRatio,\n            false,\n            false,\n            k,\n            that.option.series[selectedIndex].pieData.value\n          )\n          that.option.series[selectedIndex].pieStatus.selected = false\n        }\n        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）\n        that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,\n          isSelected,\n          isHovered, k, that.option.series[params.seriesIndex].pieData.value)\n        that.option.series[params.seriesIndex].pieStatus.selected = isSelected\n        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex\n        if (isSelected) {\n          selectedIndex = params.seriesIndex\n        }\n        // 使用优化的更新方法\n        that.updateChartOption(that.option)\n      })\n\n      // 防抖的mouseover处理\n      const debouncedMouseover = this.debounce((params) => {\n        that.handleMouseover(params, hoveredIndex, (newIndex) => {\n          hoveredIndex = newIndex\n        })\n      }, 16) // 约60fps\n\n      // 监听 mouseover，近似实现高亮（放大）效果\n      myChart.on('mouseover', debouncedMouseover)\n\n      // 修正取消高亮失败的 bug\n      myChart.on('globalout', function () {\n        if (hoveredIndex !== '') {\n          const isSelected = that.option.series[hoveredIndex].pieStatus.selected\n          const isHovered = false\n          const k = that.option.series[hoveredIndex].pieStatus.k\n          const startRatio = that.option.series[hoveredIndex].pieData.startRatio\n          const endRatio = that.option.series[hoveredIndex].pieData.endRatio\n\n          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(\n            startRatio, endRatio, isSelected, isHovered, k,\n            that.option.series[hoveredIndex].pieData.value\n          )\n          that.option.series[hoveredIndex].pieStatus.hovered = isHovered\n          hoveredIndex = ''\n\n          // 使用优化的更新方法\n          that.updateChartOption(that.option)\n        }\n      })\n    },\n\n    // 提取mouseover处理逻辑\n    handleMouseover (params, hoveredIndex, setHoveredIndex) {\n      // 如果触发 mouseover 的扇形当前已高亮，则不做操作\n      if (hoveredIndex === params.seriesIndex) {\n        return // 已经高亮，不做操作\n      }\n\n      // 准备重新渲染扇形所需的参数\n      let isSelected, isHovered, startRatio, endRatio, k\n\n      // 如果当前有高亮的扇形，取消其高亮状态\n      if (hoveredIndex !== '') {\n        isSelected = this.option.series[hoveredIndex].pieStatus.selected\n        isHovered = false\n        startRatio = this.option.series[hoveredIndex].pieData.startRatio\n        endRatio = this.option.series[hoveredIndex].pieData.endRatio\n        k = this.option.series[hoveredIndex].pieStatus.k\n\n        this.option.series[hoveredIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[hoveredIndex].pieData.value\n        )\n        this.option.series[hoveredIndex].pieStatus.hovered = isHovered\n      }\n\n      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮\n      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n        isSelected = this.option.series[params.seriesIndex].pieStatus.selected\n        isHovered = true\n        startRatio = this.option.series[params.seriesIndex].pieData.startRatio\n        endRatio = this.option.series[params.seriesIndex].pieData.endRatio\n        k = this.option.series[params.seriesIndex].pieStatus.k\n\n        this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[params.seriesIndex].pieData.value + 5\n        )\n        this.option.series[params.seriesIndex].pieStatus.hovered = isHovered\n        setHoveredIndex(params.seriesIndex)\n      }\n\n      // 使用优化的更新方法\n      this.updateChartOption(this.option)\n    },\n\n    // 这是一个自定义计算的方法\n    fomatFloat (num, n) {\n      var f = parseFloat(num)\n      if (isNaN(f)) {\n        return false\n      }\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂\n      var s = f.toString()\n      var rs = s.indexOf('.')\n      // 判定如果是整数，增加小数点再补0\n      if (rs < 0) {\n        rs = s.length\n        s += '.'\n      }\n      while (s.length <= rs + n) {\n        s += '0'\n      }\n      return s\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.party-distribution-chart {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图(外面的容器)\n.container {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图的大小\n.chartsGl {\n  height: 100%;\n  width: 100%;\n}\n</style>\n"]}]}