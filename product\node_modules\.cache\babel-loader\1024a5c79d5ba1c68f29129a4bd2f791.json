{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart.vue", "mtime": 1758770115856}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": ";AAKA;AAEA;EACAA,gBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAJ;MACAG,YADA;MAEAC;IAFA,CALA;IASAC;MACAF,YADA;MAEAG,WAFA;MAGAF;IAHA,CATA;IAcAG;MACAJ,YADA;MAEAG,WAFA;MAGAF;IAHA,CAdA;IAmBAI;MACAL,YADA;MAEAG,UAFA;MAGAF;IAHA,CAnBA;IAwBAK;MACAN,YADA;MAEAG,UAFA;MAGAF;IAHA,CAxBA;IA6BAM;MACAP,WADA;MAEAC,eAFA;MAGAE;IAHA,CA7BA;IAkCAK;MACAR,WADA;MAEAC,eAFA;MAGAE;IAHA,CAlCA;IAuCAM;MACAT,WADA;MAEAC,eAFA;MAGAE;IAHA,CAvCA;IA4CAO;MACAV,WADA;MAEAC,eAFA;MAGAE;IAHA,CA5CA;IAiDAQ;MACAX,YADA;MAEAG,cAFA;MAGAF;IAHA,CAjDA;IAsDAW;MACAZ,YADA;MAEAG,cAFA;MAGAF;IAHA,CAtDA;IA2DAY;MACAb,YADA;MAEAG,WAFA;MAGAF;IAHA,CA3DA;IAgEAa;MACAd,WADA;MAEAC,cAFA;MAGAE;IAHA,CAhEA;IAqEAY;MACAf,aADA;MAEAG,aAFA;MAGAF;IAHA;EArEA,CAFA;;EA6EAe;IACA;MACAC,WADA;MAEAC,wBAFA;MAEA;MACAC,yBAHA;MAGA;MACA;MACAC,SACA,SADA,EACA,SADA,EACA,SADA,EACA,SADA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAEA,SAFA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAGA,SAHA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAIA,SAJA,EAKA,SALA,EAKA,SALA,EAKA,SALA,EAKA,SALA;IALA;EAaA,CA3FA;;EA4FAC;IACA;EACA,CA9FA;;EA+FAC;IACA;IACA;MACAC;IACA;;IACA;MACA;IACA;EACA,CAvGA;;EAwGAC;IACAC;MACA;MACA;QACA;MACA,CAJA,CAKA;;;MACA;IACA,CARA;;IASAC;MACA;MACA;MACA;MACA;QACAC;UACAC,eADA;UAEAC,qCAFA;UAGAC,qCAHA;UAIAC,sBAJA;UAKAC,cALA;UAMAC;YACAC;UADA;QANA,CADA;QAWAC;UACAC,qBADA;UAEAC,uEAFA;UAGAC,yIAHA;UAIAC,2DAJA;UAKAC,iGALA;UAMAC,uDANA;UAOAC,+BAPA;UAQAC,iCARA;UASAC,qBATA;UAUAC,2BAVA;UAWAZ;YACAC,aADA;YAEAY,YAFA;YAGAC;UAHA,CAXA;UAgBAlB;YACA;;YACA;cACA;YACA,CAFA,MAEA;cACA;cACA,wEAFA,CAGA;;cACA;cACA;YACA;UACA;QA3BA,CAXA;QAwCAmB,SACA;UACAnD,eADA;UAEAG,WAFA;UAGAO,mBAHA;UAIAC,mBAJA;UAKAyC,wBALA;UAMAC;YACAC,WADA;YAEAC;UAFA,CANA;UAUAC;YACAjB,UADA;YAEAkB,kBAFA;YAGAR,YAHA;YAIAZ,aAJA;YAKAL;cACA;gBACA;cACA;;cACA;YACA,CAVA;YAWA0B;cACAC;gBACAV,YADA;gBAEAW,kBAFA;gBAGAvB,gBAHA;gBAIAwB;cAJA,CADA;cAOAL;gBACAP,YADA;gBAEAZ,gBAFA;gBAGAwB;cAHA;YAPA;UAXA,CAVA;UAmCAC;YACAvB;UADA,CAnCA;UAsCAwB;YACA5B,oCADA;YAEAD,sBAFA,CAEA;;UAFA,CAtCA;UA0CAf;YACAwC,iBADA;YAEA3D,eAFA;YAGA+D;cAAA1B;YAAA;UAHA;QA1CA,CADA,EAiDA;UACAlC,WADA;UAEAO,uBAFA;UAGAC,uBAHA;UAIAQ,OACA;YACAwC,UADA;YAEAI;cACA1B;YADA;UAFA,CADA,CAJA;UAYAmB;YACAjB;UADA;QAZA,KAeA,EAfA,CAjDA,CAxCA;QA0GAyB,4BACA;UACA7D,cADA;UAEAuC,sBAFA;UAGAC,oBAHA;UAIAsB;YACAC,KADA;YAEAC,KAFA;YAGAC;UAHA,CAJA;UASAC;YACAC,YADA;YAEAC,sDAFA;YAGAC;cACArE,cADA;cAEAsE,IAFA;cAGAC,IAHA;cAIAC,KAJA;cAKAC,KALA;cAMAC,aACA;gBAAAC;gBAAAzC;cAAA,CADA,EAEA;gBAAAyC;gBAAAzC;cAAA,CAFA;YANA;UAHA,CATA;UAwBA0C,KAxBA;UAyBAC,YAzBA;UA0BAvB;QA1BA,CADA,IA6BA;MAvIA;MAyIA,6BA7IA,CA8IA;;MACAwB;QACA;UACA;QACA;MACA,CAJA,EA/IA,CAoJA;;MACA,0BArJA,CAsJA;;MACA;QACA;MACA,CAFA;MAGA;QACA;MACA,CAFA;IAGA,CAtKA;;IAwKA;IACAC;MACA;MACA;QACA;QACA;UACA;YACA/E,gBADA;YAEAgF,cAFA;YAGAC;UAHA,GADA,CAMA;;UACA;YACAjF;UADA;QAGA,CAZA,CAcA;;;QACA;QACA;UACAA,iBADA;UAEAgF,cAFA;UAGAC;QAHA,GAhBA,CAsBA;;QACA;UACAjF,eADA;UAEAgF,cAFA;UAGAC;QAHA;MAKA,CA5BA,EA4BA,IA5BA,EAFA,CA8BA;IACA,CAxMA;;IA0MA;IACAC;MACA;QACA3D;QACA;MACA,CAJA,CAKA;;;MACA;QACA;UACAvB,gBADA;UAEAgF,cAFA;UAGAC;QAHA;QAKA;MACA;IACA;;EAzNA;AAxGA", "names": ["name", "props", "id", "type", "required", "legendIcon", "default", "legendItemGap", "legend<PERSON><PERSON><PERSON><PERSON><PERSON>", "legendItemHeight", "radius", "center", "lineRadius", "lineCenter", "graphicLeft", "graphicTop", "graphicShapeR", "chartData", "legendShow", "data", "chart", "autoHighlightTimer", "currentHighlightIndex", "colors", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "getColor", "initChart", "tooltip", "trigger", "formatter", "backgroundColor", "borderColor", "borderWidth", "textStyle", "color", "legend", "show", "orient", "right", "left", "top", "bottom", "itemWidth", "itemHeight", "icon", "itemGap", "fontSize", "fontFamily", "series", "avoidLabelOverlap", "emphasis", "scale", "scaleSize", "label", "position", "rich", "value", "fontWeight", "lineHeight", "labelLine", "itemStyle", "graphic", "shape", "cx", "cy", "r", "style", "fill", "lineWidth", "stroke", "x", "y", "x2", "y2", "colorStops", "offset", "z", "silent", "window", "startAutoHighlight", "seriesIndex", "dataIndex", "stopAutoHighlight"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["PieChart.vue"], "sourcesContent": ["<template>\n  <div :id=\"id\" class=\"pie-chart\"></div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: '<PERSON><PERSON><PERSON>',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    name: {\n      type: String,\n      required: true\n    },\n    legendIcon: {\n      type: String,\n      default: '',\n      required: false\n    },\n    legendItemGap: {\n      type: Number,\n      default: 25,\n      required: false\n    },\n    legendItemWidth: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    legendItemHeight: {\n      type: Number,\n      default: 5,\n      required: false\n    },\n    radius: {\n      type: Array,\n      required: false,\n      default: () => ['55%', '80%']\n    },\n    center: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    lineRadius: {\n      type: Array,\n      required: false,\n      default: () => ['88%', '89%']\n    },\n    lineCenter: {\n      type: Array,\n      required: false,\n      default: () => ['30%', '50%']\n    },\n    graphicLeft: {\n      type: String,\n      default: '17%',\n      required: false\n    },\n    graphicTop: {\n      type: String,\n      default: '26%',\n      required: false\n    },\n    graphicShapeR: {\n      type: Number,\n      default: 50,\n      required: false\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    },\n    legendShow: {\n      type: Boolean,\n      default: true,\n      required: false\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      autoHighlightTimer: null, // 自动高亮定时器\n      currentHighlightIndex: -1, // 当前高亮的数据项索引\n      // 预定义的颜色数组，按顺序分配给数据项\n      colors: [\n        '#4FC3F7', '#FFF67C', '#66BB6A', '#FFA726',\n        '#FF7043', '#AB47BC', '#5C6BC0', '#42A5F5',\n        '#FFCA28', '#4CAF50', '#EF5350', '#8D6E63',\n        '#9C27B0', '#3F51B5', '#2196F3', '#00BCD4',\n        '#FF9800', '#795548', '#607D8B', '#E91E63'\n      ]\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    // 清除自动高亮定时器\n    if (this.autoHighlightTimer) {\n      clearInterval(this.autoHighlightTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n  },\n  methods: {\n    getColor (index, item) {\n      // 如果数据项中有颜色信息，优先使用数据中的颜色\n      if (item && item.color) {\n        return item.color\n      }\n      // 否则使用预定义的颜色数组\n      return this.colors[index % this.colors.length]\n    },\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n      this.chart = echarts.init(chartContainer)\n      const option = {\n        tooltip: {\n          trigger: 'item',\n          formatter: '{a} <br/>{b}: {c} ({d}%)',\n          backgroundColor: 'rgba(0, 0, 0, 0.8)',\n          borderColor: '#00d4ff',\n          borderWidth: 1,\n          textStyle: {\n            color: '#fff'\n          }\n        },\n        legend: {\n          show: this.legendShow,\n          orient: this.id === 'category_distribution' ? 'horizontal' : 'vertical',\n          right: this.id === 'reply-type-pie' ? '0%' : this.id === 'category_distribution' ? null : this.id === 'proposal-statistics' ? '0%' : '5%',\n          left: this.id === 'category_distribution' ? 'center' : null,\n          top: this.id === 'reply-type-pie' ? '38%' : this.id === 'category_distribution' ? null : 'center',\n          bottom: this.id === 'category_distribution' ? 20 : null,\n          itemWidth: this.legendItemWidth,\n          itemHeight: this.legendItemHeight,\n          icon: this.legendIcon,\n          itemGap: this.legendItemGap,\n          textStyle: {\n            color: '#fff',\n            fontSize: 12,\n            fontFamily: 'Microsoft YaHei'\n          },\n          formatter: (name) => {\n            const item = this.chartData.find(d => d.name === name)\n            if (this.id === 'reply-type-pie') {\n              return `${name}`\n            } else {\n              // 计算总数\n              const total = this.chartData.reduce((sum, data) => sum + data.value, 0)\n              // 计算百分比\n              const percentage = item && total > 0 ? ((item.value / total) * 100).toFixed(2) : '0'\n              return `${name}  ${percentage}%`\n            }\n          }\n        },\n        series: [\n          {\n            name: this.name,\n            type: 'pie',\n            radius: this.radius,\n            center: this.center,\n            avoidLabelOverlap: false,\n            emphasis: {\n              scale: true,\n              scaleSize: 10\n            },\n            label: {\n              show: true,\n              position: 'center',\n              fontSize: 16,\n              color: '#fff',\n              formatter: () => {\n                if (!this.legendShow) {\n                  return `{value|${this.name}}\\n{label|采用率}`\n                }\n                return this.name\n              },\n              rich: {\n                value: {\n                  fontSize: 28,\n                  fontWeight: 'bold',\n                  color: '#FFFFFF',\n                  lineHeight: 35\n                },\n                label: {\n                  fontSize: 16,\n                  color: '#FFFFFF',\n                  lineHeight: 30\n                }\n              }\n            },\n            labelLine: {\n              show: false\n            },\n            itemStyle: {\n              borderWidth: this.legendShow ? 3 : 0,\n              borderColor: '#07345F' // 用大屏背景色\n            },\n            data: this.chartData.map((item, index) => ({\n              value: item.value,\n              name: item.name,\n              itemStyle: { color: this.getColor(index, item) }\n            }))\n          },\n          ...(this.legendShow ? [{\n            type: 'pie',\n            radius: this.lineRadius,\n            center: this.lineCenter,\n            data: [\n              {\n                value: 100,\n                itemStyle: {\n                  color: '#2f689a'\n                }\n              }\n            ],\n            label: {\n              show: false\n            }\n          }] : [])\n        ],\n        graphic: this.legendShow ? [\n          {\n            type: 'circle',\n            left: this.graphicLeft,\n            top: this.graphicTop,\n            shape: {\n              cx: 0,\n              cy: 0,\n              r: this.graphicShapeR\n            },\n            style: {\n              fill: 'none',\n              lineWidth: this.id === 'category_distribution' ? 4 : 3,\n              stroke: {\n                type: 'linear',\n                x: 0,\n                y: 0,\n                x2: 0,\n                y2: 1,\n                colorStops: [\n                  { offset: 0, color: '#23E1FF' },\n                  { offset: 1, color: 'rgba(35,225,255,0)' }\n                ]\n              }\n            },\n            z: 10,\n            silent: true,\n            position: [0, 0]\n          }\n        ] : []\n      }\n      this.chart.setOption(option)\n      // 监听窗口大小变化\n      window.addEventListener('resize', () => {\n        if (this.chart) {\n          this.chart.resize()\n        }\n      })\n      // 启动自动高亮效果\n      this.startAutoHighlight()\n      // 添加鼠标事件监听\n      this.chart.on('mouseover', () => {\n        this.stopAutoHighlight()\n      })\n      this.chart.on('mouseout', () => {\n        this.startAutoHighlight()\n      })\n    },\n\n    // 开始自动高亮效果\n    startAutoHighlight () {\n      if (this.chartData.length === 0) return\n      this.autoHighlightTimer = setInterval(() => {\n        // 取消当前高亮和tooltip\n        if (this.currentHighlightIndex >= 0) {\n          this.chart.dispatchAction({\n            type: 'downplay',\n            seriesIndex: 0,\n            dataIndex: this.currentHighlightIndex\n          })\n          // 隐藏tooltip\n          this.chart.dispatchAction({\n            type: 'hideTip'\n          })\n        }\n\n        // 高亮下一个数据项\n        this.currentHighlightIndex = (this.currentHighlightIndex + 1) % this.chartData.length\n        this.chart.dispatchAction({\n          type: 'highlight',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n\n        // 显示tooltip\n        this.chart.dispatchAction({\n          type: 'showTip',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n      }, 2000) // 每2秒切换一次\n    },\n\n    // 停止自动高亮效果\n    stopAutoHighlight () {\n      if (this.autoHighlightTimer) {\n        clearInterval(this.autoHighlightTimer)\n        this.autoHighlightTimer = null\n      }\n      // 取消所有高亮\n      if (this.chart && this.currentHighlightIndex >= 0) {\n        this.chart.dispatchAction({\n          type: 'downplay',\n          seriesIndex: 0,\n          dataIndex: this.currentHighlightIndex\n        })\n        this.currentHighlightIndex = -1\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.pie-chart {\n  width: 100%;\n  height: 100%;\n  position: relative;\n}\n</style>\n"]}]}