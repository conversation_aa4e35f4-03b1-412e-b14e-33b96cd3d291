{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue?vue&type=style&index=0&id=7bc2e329&scoped=true&lang=css&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue", "mtime": 1758776011577}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HorizontalBarChart.vue"], "names": [], "mappings": ";AAyFA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "HorizontalBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"horizontal-bar-chart\">\n    <div class=\"chart-container\">\n      <div v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-row\">\n        <div class=\"row-label\">{{ item.name }}</div>\n        <div class=\"row-bar-container\">\n          <div class=\"row-bar\">\n            <div v-for=\"(segment, segIndex) in getSegments(item.value)\" :key=\"segIndex\" class=\"bar-segment\"\n              :class=\"{ 'filled': segIndex < getFilledSegments(item.value) }\"></div>\n          </div>\n          <div class=\"row-value\">{{ item.value }}人</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HorizontalBarChart',\n  props: {\n    chartData: {\n      type: Array,\n      default: () => []\n    },\n    // 每个分段代表的数值\n    segmentValue: {\n      type: Number,\n      default: 10\n    },\n    // 最大分段数（固定总分段数，不随数据变化）\n    maxSegments: {\n      type: Number,\n      default: 25\n    },\n    // 进度条的最大值（用于计算比例，如果不设置则自动计算）\n    maxValue: {\n      type: Number,\n      default: null\n    },\n    // 最大值的缩放比例（数据最大值的倍数）\n    maxValueScale: {\n      type: Number,\n      default: 1.1\n    }\n  },\n\n  computed: {\n    // 计算总分段数（固定值，不随数据变化）\n    totalSegments () {\n      return this.maxSegments\n    },\n    // 动态计算的最大值\n    dynamicMaxValue () {\n      // 如果手动设置了maxValue，则使用设置的值\n      if (this.maxValue !== null) {\n        return this.maxValue\n      }\n      // 否则根据数据自动计算\n      if (!this.chartData.length) return 100\n      const dataMaxValue = Math.max(...this.chartData.map(item => item.value))\n      // 返回数据最大值的倍数，确保最大值不会撑满\n      const calculatedMax = Math.ceil(dataMaxValue * this.maxValueScale)\n      console.log(`数据最大值: ${dataMaxValue}, 缩放比例: ${this.maxValueScale}, 计算的最大值: ${calculatedMax}`)\n      return calculatedMax\n    }\n  },\n  methods: {\n    // 生成分段数组\n    getSegments (value) {\n      const segments = []\n      for (let i = 0; i < this.totalSegments; i++) {\n        segments.push(i)\n      }\n      return segments\n    },\n    // 计算应该填充的分段数\n    getFilledSegments (value) {\n      if (value <= 0) return 0\n      // 计算按比例应该填充的分段数\n      const proportionalSegments = Math.floor((value / this.dynamicMaxValue) * this.totalSegments)\n      // 确保有数值的项目至少显示1个分段\n      return Math.max(1, proportionalSegments)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.horizontal-bar-chart {\n  width: 100%;\n  height: 100%;\n  padding-top: 10px;\n}\n\n.chart-container {\n  display: flex;\n  flex-direction: column;\n  gap: 22px;\n  min-height: fit-content;\n}\n\n.chart-row {\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.row-label {\n  width: 50px;\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: 500;\n  text-align: left;\n  flex-shrink: 0;\n  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);\n}\n\n.row-bar-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  margin-left: 15px;\n}\n\n.row-bar {\n  flex: 1;\n  height: 15px;\n  display: flex;\n  align-items: center;\n  background: #09306B;\n  border: 1px solid #979797;\n  position: relative;\n  overflow: hidden;\n  padding: 1px;\n  /* 右边斜切效果 */\n  /* clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 0 100%); */\n}\n\n.bar-segment {\n  flex: 1;\n  height: 10px;\n  background: rgba(0, 100, 200, 0.2);\n  transition: all 0.4s ease;\n  position: relative;\n  border: none;\n  /* 倾斜的格子效果 */\n  clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);\n  margin-right: 1px;\n}\n\n.bar-segment:last-child {\n  margin-right: 0;\n}\n\n.bar-segment.filled {\n  background: linear-gradient(135deg,\n      #00D4FF 0%,\n      #0099CC 30%,\n      #00AADD 70%,\n      #33E0FF 100%);\n}\n\n.row-value {\n  color: #FFFFFF;\n  font-size: 14px;\n  width: 70px;\n  text-align: right;\n}\n\n/* 悬停效果 */\n.chart-row:hover .row-bar {\n  border-color: rgba(0, 212, 255, 0.8);\n  box-shadow:\n    inset 0 1px 2px rgba(0, 0, 0, 0.3),\n    0 0 15px rgba(0, 212, 255, 0.4);\n}\n\n.chart-row:hover .bar-segment.filled {\n  background: linear-gradient(135deg,\n      #33E0FF 0%,\n      #00AADD 30%,\n      #00BBEE 70%,\n      #55F0FF 100%);\n  box-shadow:\n    0 0 12px rgba(0, 212, 255, 0.7),\n    inset 0 1px 2px rgba(255, 255, 255, 0.4);\n  transform: scale(1.05);\n}\n\n.chart-row:hover .row-value {\n  color: #00D4FF;\n  text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);\n}\n\n.chart-row:hover .row-label {\n  color: #00D4FF;\n  text-shadow: 0 0 6px rgba(0, 212, 255, 0.4);\n}\n</style>\n"]}]}