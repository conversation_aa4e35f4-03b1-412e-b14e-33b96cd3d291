{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--13-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=template&id=76285fab&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1758767185562}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1655712169000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "ref", "staticClass", "_v", "_s", "currentTime", "_m", "on", "click", "handleCommitteeClick", "_l", "committeeStatisticsNum", "item", "index", "key", "attrs", "src", "icon", "alt", "label", "style", "color", "value", "id", "showCount", "committeeBarData", "handleProposalClick", "proposalStatisticsNum", "unit", "proposalChartData", "name", "proposalChartName", "legendIcon", "legendItemGap", "radius", "center", "lineRadius", "lineCenter", "graphicLeft", "graphicTop", "graphicShapeR", "workDynamicsData", "class", "title", "publishDate", "data", "mapData", "areaId", "areaName", "handleRegionClick", "handlePerformanceClick", "performanceData", "userName", "meeting", "proposal", "social", "advise", "activity", "others", "handlePublicOpinionClick", "socialData", "memberSubmit", "count", "adopted", "total", "unitSubmit", "conferenceActivitiesData", "getItemClass", "handleNetWorkClick", "discussionsData", "statistics", "hotTopics", "topic", "staticRenderFns", "staticStyle", "height", "require", "_withStripped"], "sources": ["D:/zy/xm/pc/qdzx/product/src/views/smartBrainLargeScreen/home/<USER>"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\"div\", { ref: \"bigScreen\", staticClass: \"big-screen\" }, [\n    _c(\"div\", { staticClass: \"screen-header\" }, [\n      _c(\"div\", { staticClass: \"header-left\" }, [\n        _c(\"span\", { staticClass: \"date-time\" }, [\n          _vm._v(_vm._s(_vm.currentTime)),\n        ]),\n        _c(\"span\", { staticClass: \"weather\" }, [_vm._v(\"晴 24℃ 东南风\")]),\n      ]),\n      _vm._m(0),\n      _c(\"div\", { staticClass: \"header-right\" }),\n    ]),\n    _c(\"div\", { staticClass: \"screen-content\" }, [\n      _c(\"div\", { staticClass: \"left-panel\" }, [\n        _c(\"div\", { staticClass: \"committee_statistics\" }, [\n          _c(\"div\", { staticClass: \"header_box\" }, [\n            _c(\n              \"span\",\n              {\n                staticClass: \"header_text_left\",\n                on: { click: _vm.handleCommitteeClick },\n              },\n              [_vm._v(\"委员统计\")]\n            ),\n            _c(\"span\", { staticClass: \"header_text_right\" }, [\n              _vm._v(\"十二届二次\"),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"committee_statistics_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"committee_statistics_num\" },\n              _vm._l(_vm.committeeStatisticsNum, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"num_box\" }, [\n                  _c(\"img\", {\n                    staticClass: \"num_icon\",\n                    attrs: { src: item.icon, alt: \"\" },\n                  }),\n                  _c(\"div\", [\n                    _c(\"div\", { staticClass: \"num_label\" }, [\n                      _vm._v(_vm._s(item.label)),\n                    ]),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"num_value\",\n                        style: `color:${item.color}`,\n                      },\n                      [_vm._v(_vm._s(item.value))]\n                    ),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"committee_statistics_chart\" },\n              [\n                _c(\"BarScrollChart\", {\n                  attrs: {\n                    id: \"committee-statistics\",\n                    showCount: 5,\n                    \"chart-data\": _vm.committeeBarData,\n                  },\n                }),\n              ],\n              1\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"proposal_statistics\" }, [\n          _c(\"div\", { staticClass: \"header_box\" }, [\n            _c(\n              \"span\",\n              {\n                staticClass: \"header_text_left\",\n                on: { click: _vm.handleProposalClick },\n              },\n              [_vm._v(\"提案统计\")]\n            ),\n            _c(\"span\", { staticClass: \"header_text_right\" }, [\n              _vm._v(\"十二届二次会议\"),\n            ]),\n            _vm._m(1),\n          ]),\n          _c(\"div\", { staticClass: \"proposal_statistics_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"proposal_statistics_num\" },\n              _vm._l(_vm.proposalStatisticsNum, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"num_box\" }, [\n                  _c(\"img\", {\n                    staticClass: \"num_icon\",\n                    attrs: { src: item.icon, alt: \"\" },\n                  }),\n                  _c(\"div\", [\n                    _c(\"div\", { staticClass: \"num_label\" }, [\n                      _vm._v(_vm._s(item.label)),\n                    ]),\n                    _c(\n                      \"div\",\n                      {\n                        staticClass: \"num_value\",\n                        style: `color:${item.color}`,\n                      },\n                      [\n                        _vm._v(_vm._s(item.value)),\n                        _c(\"span\", { staticClass: \"num_unit\" }, [\n                          _vm._v(_vm._s(item.unit)),\n                        ]),\n                      ]\n                    ),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\n              \"div\",\n              { staticClass: \"proposal_statistics_chart\" },\n              [\n                _c(\"PieChart\", {\n                  attrs: {\n                    id: \"proposal-statistics\",\n                    \"chart-data\": _vm.proposalChartData,\n                    name: _vm.proposalChartName,\n                    legendIcon: \"circle\",\n                    legendItemGap: 12,\n                    radius: [\"60%\", \"85%\"],\n                    center: [\"22%\", \"50%\"],\n                    lineRadius: [\"94%\", \"95%\"],\n                    lineCenter: [\"22%\", \"50%\"],\n                    graphicLeft: \"12%\",\n                    graphicTop: \"23%\",\n                    graphicShapeR: 40,\n                  },\n                }),\n              ],\n              1\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"work_dynamics\" }, [\n          _vm._m(2),\n          _c(\"div\", { staticClass: \"work_dynamics_content\" }, [\n            _c(\n              \"div\",\n              { ref: \"dynamicsList\", staticClass: \"dynamics-list\" },\n              _vm._l(_vm.workDynamicsData, function (item, index) {\n                return _c(\n                  \"div\",\n                  {\n                    key: item.id,\n                    staticClass: \"dynamics-item\",\n                    class: {\n                      \"with-bg-image\": index % 2 === 0,\n                      \"with-bg-color\": index % 2 === 1,\n                    },\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"dynamics-content\" }, [\n                      _c(\"div\", { staticClass: \"dynamics-title\" }, [\n                        _vm._v(_vm._s(item.title)),\n                      ]),\n                      _c(\"div\", { staticClass: \"dynamics-date\" }, [\n                        _vm._v(_vm._s(item.publishDate)),\n                      ]),\n                    ]),\n                  ]\n                )\n              }),\n              0\n            ),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"center-panel\" }, [\n        _c(\n          \"div\",\n          { staticClass: \"map_box\" },\n          [\n            _c(\"MapComponent\", {\n              attrs: {\n                data: _vm.mapData,\n                areaId: _vm.areaId + \"\",\n                areaName: _vm.areaName,\n              },\n              on: { \"region-click\": _vm.handleRegionClick },\n            }),\n          ],\n          1\n        ),\n        _c(\"div\", { staticClass: \"performance_statistics\" }, [\n          _c(\"div\", { staticClass: \"header_box\" }, [\n            _c(\n              \"span\",\n              {\n                staticClass: \"header_text_left\",\n                on: { click: _vm.handlePerformanceClick },\n              },\n              [_vm._v(\"履职统计\")]\n            ),\n            _c(\"span\", { staticClass: \"header_text_right\" }, [\n              _vm._v(\"十二届二次\"),\n            ]),\n          ]),\n          _c(\"div\", { staticClass: \"performance_statistics_content\" }, [\n            _c(\"div\", { staticClass: \"table-container\" }, [\n              _vm._m(3),\n              _c(\n                \"div\",\n                { ref: \"performanceTableBody\", staticClass: \"table-body\" },\n                _vm._l(_vm.performanceData, function (item, index) {\n                  return _c(\"div\", { key: index, staticClass: \"table-row\" }, [\n                    _c(\"div\", { staticClass: \"table-cell name-col\" }, [\n                      _vm._v(_vm._s(item.userName)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell meeting-col\" }, [\n                      _vm._v(_vm._s(item.meeting)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell proposal-col\" }, [\n                      _vm._v(_vm._s(item.proposal)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell opinion-col\" }, [\n                      _vm._v(_vm._s(item.social)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell suggestion-col\" }, [\n                      _vm._v(_vm._s(item.advise) + \" \"),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell reading-col\" }, [\n                      _vm._v(_vm._s(item.activity)),\n                    ]),\n                    _c(\"div\", { staticClass: \"table-cell training-col\" }, [\n                      _vm._v(_vm._s(item.others)),\n                    ]),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n      _c(\"div\", { staticClass: \"right-panel\" }, [\n        _c(\"div\", { staticClass: \"social\" }, [\n          _c(\"div\", { staticClass: \"header_box\" }, [\n            _c(\n              \"span\",\n              {\n                staticClass: \"header_text_left\",\n                on: { click: _vm.handlePublicOpinionClick },\n              },\n              [_vm._v(\"社情民意\")]\n            ),\n            _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n          ]),\n          _c(\"div\", { staticClass: \"social_content\" }, [\n            _c(\"div\", { staticClass: \"social-data-container\" }, [\n              _c(\"div\", { staticClass: \"left-data-item\" }, [\n                _c(\"div\", { staticClass: \"left-data-label\" }, [\n                  _vm._v(\"委员报送\"),\n                ]),\n                _c(\"div\", { staticClass: \"left-data-value\" }, [\n                  _vm._v(\"总数\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.memberSubmit.count)),\n                  ]),\n                  _vm._v(\"篇\"),\n                ]),\n                _c(\"div\", { staticClass: \"left-data-detail\" }, [\n                  _vm._v(\"采用\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.memberSubmit.adopted)),\n                  ]),\n                  _vm._v(\" 篇\"),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"center-chart\" }, [\n                _c(\"div\", { staticClass: \"progress-content\" }, [\n                  _c(\"div\", { staticClass: \"total-number\" }, [\n                    _vm._v(_vm._s(_vm.socialData.total)),\n                  ]),\n                  _c(\"div\", { staticClass: \"total-label\" }, [_vm._v(\"总数\")]),\n                ]),\n              ]),\n              _c(\"div\", { staticClass: \"right-data-item\" }, [\n                _c(\"div\", { staticClass: \"right-data-label\" }, [\n                  _vm._v(\"单位报送\"),\n                ]),\n                _c(\"div\", { staticClass: \"right-data-value\" }, [\n                  _vm._v(\"总数\"),\n                  _c(\"span\", [_vm._v(_vm._s(_vm.socialData.unitSubmit.count))]),\n                  _vm._v(\"篇\"),\n                ]),\n                _c(\"div\", { staticClass: \"right-data-detail\" }, [\n                  _vm._v(\"采用\"),\n                  _c(\"span\", [\n                    _vm._v(_vm._s(_vm.socialData.unitSubmit.adopted)),\n                  ]),\n                  _vm._v(\" 篇\"),\n                ]),\n              ]),\n            ]),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"conference_activities\" }, [\n          _vm._m(4),\n          _c(\"div\", { staticClass: \"conference_activities_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"activities-grid\" },\n              _vm._l(_vm.conferenceActivitiesData, function (item, index) {\n                return _c(\n                  \"div\",\n                  {\n                    key: index,\n                    staticClass: \"activity-item\",\n                    class: _vm.getItemClass(item.name),\n                  },\n                  [\n                    _c(\"div\", { staticClass: \"activity-value\" }, [\n                      _vm._v(_vm._s(item.value)),\n                    ]),\n                    _c(\"div\", { staticClass: \"activity-name\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                  ]\n                )\n              }),\n              0\n            ),\n          ]),\n        ]),\n        _c(\"div\", { staticClass: \"discussions\" }, [\n          _c(\"div\", { staticClass: \"header_box\" }, [\n            _c(\n              \"span\",\n              {\n                staticClass: \"header_text_left\",\n                on: { click: _vm.handleNetWorkClick },\n              },\n              [_vm._v(\"网络议政\")]\n            ),\n            _c(\"span\", { staticClass: \"header_text_right\" }),\n          ]),\n          _c(\"div\", { staticClass: \"discussions_content\" }, [\n            _c(\n              \"div\",\n              { staticClass: \"statistics-section\" },\n              _vm._l(_vm.discussionsData.statistics, function (item, index) {\n                return _c(\"div\", { key: index, staticClass: \"stat-item\" }, [\n                  _c(\"div\", { staticClass: \"stat-dot\" }),\n                  _c(\"div\", { staticClass: \"stat-info\" }, [\n                    _c(\"span\", { staticClass: \"stat-name\" }, [\n                      _vm._v(_vm._s(item.name)),\n                    ]),\n                    _c(\"span\", { staticClass: \"stat-value\" }, [\n                      _vm._v(_vm._s(item.value)),\n                    ]),\n                    _c(\"span\", { staticClass: \"stat-unit\" }, [\n                      _vm._v(_vm._s(item.unit)),\n                    ]),\n                  ]),\n                ])\n              }),\n              0\n            ),\n            _c(\"div\", { staticClass: \"hot-topics-section\" }, [\n              _vm._m(5),\n              _c(\n                \"div\",\n                { staticClass: \"topics-list\" },\n                _vm._l(_vm.discussionsData.hotTopics, function (topic, index) {\n                  return _c(\"div\", { key: index, staticClass: \"topic-item\" }, [\n                    _c(\"div\", { staticClass: \"topic-dot\" }),\n                    _c(\"span\", { staticClass: \"topic-text\" }, [\n                      _vm._v(_vm._s(topic.title)),\n                    ]),\n                  ])\n                }),\n                0\n              ),\n            ]),\n          ]),\n        ]),\n      ]),\n    ]),\n  ])\n}\nvar staticRenderFns = [\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header-center\" }, [\n      _c(\"img\", {\n        staticStyle: { height: \"50px\" },\n        attrs: {\n          src: require(\"../../../assets/largeScreen/top_header_txt.png\"),\n          alt: \"\",\n        },\n      }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"span\", { staticClass: \"header_text_center\" }, [\n      _vm._v(\"提交提案总数：\"),\n      _c(\"span\", [_vm._v(\"873\")]),\n      _vm._v(\"件\"),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"工作动态\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"table-header\" }, [\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"姓名\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"会议活动\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"政协提案\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"社情民意\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"议政建言\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"读书心得\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }, [_vm._v(\"委员培训\")]),\n      _c(\"div\", { staticClass: \"header-cell\" }),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"header_box\" }, [\n      _c(\"span\", { staticClass: \"header_text_left\" }, [_vm._v(\"会议活动\")]),\n      _c(\"span\", { staticClass: \"header_text_right\" }, [_vm._v(\"本年\")]),\n    ])\n  },\n  function () {\n    var _vm = this,\n      _c = _vm._self._c\n    return _c(\"div\", { staticClass: \"hot-topics-header\" }, [\n      _c(\"img\", {\n        staticClass: \"hot-icon\",\n        attrs: {\n          src: require(\"../../../assets/largeScreen/icon_hot.png\"),\n          alt: \"热门\",\n        },\n      }),\n      _c(\"span\", { staticClass: \"hot-title\" }, [_vm._v(\"最热话题\")]),\n    ])\n  },\n]\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAT,GAAkB;EAC7B,IAAIC,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEE,GAAG,EAAE,WAAP;IAAoBC,WAAW,EAAE;EAAjC,CAAR,EAAyD,CAChEH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAACO,WAAX,CAAP,CADuC,CAAvC,CADsC,EAIxCN,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAqC,CAACJ,GAAG,CAACK,EAAJ,CAAO,WAAP,CAAD,CAArC,CAJsC,CAAxC,CADwC,EAO1CL,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAP0C,EAQ1CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,CARwC,CAA1C,CAD8D,EAWhEH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAiD,CACjDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CACA,MADA,EAEA;IACEG,WAAW,EAAE,kBADf;IAEEK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACW;IAAb;EAFN,CAFA,EAMA,CAACX,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CANA,CADqC,EASvCJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAC/CJ,GAAG,CAACK,EAAJ,CAAO,OAAP,CAD+C,CAA/C,CATqC,CAAvC,CAD+C,EAcjDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyD,CACzDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACa,sBAAX,EAAmC,UAAUC,IAAV,EAAgBC,KAAhB,EAAuB;IACxD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAgD,CACvDH,EAAE,CAAC,KAAD,EAAQ;MACRG,WAAW,EAAE,UADL;MAERa,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,IAAZ;QAAkBC,GAAG,EAAE;MAAvB;IAFC,CAAR,CADqD,EAKvDnB,EAAE,CAAC,KAAD,EAAQ,CACRA,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACO,KAAZ,CAAP,CADsC,CAAtC,CADM,EAIRpB,EAAE,CACA,KADA,EAEA;MACEG,WAAW,EAAE,WADf;MAEEkB,KAAK,EAAG,SAAQR,IAAI,CAACS,KAAM;IAF7B,CAFA,EAMA,CAACvB,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACU,KAAZ,CAAP,CAAD,CANA,CAJM,CAAR,CALqD,CAAhD,CAAT;EAmBD,CApBD,CAHA,EAwBA,CAxBA,CADuD,EA2BzDvB,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,gBAAD,EAAmB;IACnBgB,KAAK,EAAE;MACLQ,EAAE,EAAE,sBADC;MAELC,SAAS,EAAE,CAFN;MAGL,cAAc1B,GAAG,CAAC2B;IAHb;EADY,CAAnB,CADJ,CAHA,EAYA,CAZA,CA3BuD,CAAzD,CAd+C,CAAjD,CADqC,EA0DvC1B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CACA,MADA,EAEA;IACEG,WAAW,EAAE,kBADf;IAEEK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAAC4B;IAAb;EAFN,CAFA,EAMA,CAAC5B,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CANA,CADqC,EASvCJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAC/CJ,GAAG,CAACK,EAAJ,CAAO,SAAP,CAD+C,CAA/C,CATqC,EAYvCL,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAZuC,CAAvC,CAD8C,EAehDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwD,CACxDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAAC6B,qBAAX,EAAkC,UAAUf,IAAV,EAAgBC,KAAhB,EAAuB;IACvD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAgD,CACvDH,EAAE,CAAC,KAAD,EAAQ;MACRG,WAAW,EAAE,UADL;MAERa,KAAK,EAAE;QAAEC,GAAG,EAAEJ,IAAI,CAACK,IAAZ;QAAkBC,GAAG,EAAE;MAAvB;IAFC,CAAR,CADqD,EAKvDnB,EAAE,CAAC,KAAD,EAAQ,CACRA,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACO,KAAZ,CAAP,CADsC,CAAtC,CADM,EAIRpB,EAAE,CACA,KADA,EAEA;MACEG,WAAW,EAAE,WADf;MAEEkB,KAAK,EAAG,SAAQR,IAAI,CAACS,KAAM;IAF7B,CAFA,EAMA,CACEvB,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACU,KAAZ,CAAP,CADF,EAEEvB,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAsC,CACtCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACgB,IAAZ,CAAP,CADsC,CAAtC,CAFJ,CANA,CAJM,CAAR,CALqD,CAAhD,CAAT;EAwBD,CAzBD,CAHA,EA6BA,CA7BA,CADsD,EAgCxD7B,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,UAAD,EAAa;IACbgB,KAAK,EAAE;MACLQ,EAAE,EAAE,qBADC;MAEL,cAAczB,GAAG,CAAC+B,iBAFb;MAGLC,IAAI,EAAEhC,GAAG,CAACiC,iBAHL;MAILC,UAAU,EAAE,QAJP;MAKLC,aAAa,EAAE,EALV;MAMLC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,CANH;MAOLC,MAAM,EAAE,CAAC,KAAD,EAAQ,KAAR,CAPH;MAQLC,UAAU,EAAE,CAAC,KAAD,EAAQ,KAAR,CARP;MASLC,UAAU,EAAE,CAAC,KAAD,EAAQ,KAAR,CATP;MAULC,WAAW,EAAE,KAVR;MAWLC,UAAU,EAAE,KAXP;MAYLC,aAAa,EAAE;IAZV;EADM,CAAb,CADJ,CAHA,EAqBA,CArBA,CAhCsD,CAAxD,CAf8C,CAAhD,CA1DqC,EAkIvCzC,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD0C,EAE1CP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDH,EAAE,CACA,KADA,EAEA;IAAEE,GAAG,EAAE,cAAP;IAAuBC,WAAW,EAAE;EAApC,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAAC2C,gBAAX,EAA6B,UAAU7B,IAAV,EAAgBC,KAAhB,EAAuB;IAClD,OAAOd,EAAE,CACP,KADO,EAEP;MACEe,GAAG,EAAEF,IAAI,CAACW,EADZ;MAEErB,WAAW,EAAE,eAFf;MAGEwC,KAAK,EAAE;QACL,iBAAiB7B,KAAK,GAAG,CAAR,KAAc,CAD1B;QAEL,iBAAiBA,KAAK,GAAG,CAAR,KAAc;MAF1B;IAHT,CAFO,EAUP,CACEd,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA6C,CAC7CH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC+B,KAAZ,CAAP,CAD2C,CAA3C,CAD2C,EAI7C5C,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACgC,WAAZ,CAAP,CAD0C,CAA1C,CAJ2C,CAA7C,CADJ,CAVO,CAAT;EAqBD,CAtBD,CAHA,EA0BA,CA1BA,CADgD,CAAlD,CAFwC,CAA1C,CAlIqC,CAAvC,CADyC,EAqK3C7C,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGA,CACEH,EAAE,CAAC,cAAD,EAAiB;IACjBgB,KAAK,EAAE;MACL8B,IAAI,EAAE/C,GAAG,CAACgD,OADL;MAELC,MAAM,EAAEjD,GAAG,CAACiD,MAAJ,GAAa,EAFhB;MAGLC,QAAQ,EAAElD,GAAG,CAACkD;IAHT,CADU;IAMjBzC,EAAE,EAAE;MAAE,gBAAgBT,GAAG,CAACmD;IAAtB;EANa,CAAjB,CADJ,CAHA,EAaA,CAbA,CADuC,EAgBzClD,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAmD,CACnDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CACA,MADA,EAEA;IACEG,WAAW,EAAE,kBADf;IAEEK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACoD;IAAb;EAFN,CAFA,EAMA,CAACpD,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CANA,CADqC,EASvCJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAC/CJ,GAAG,CAACK,EAAJ,CAAO,OAAP,CAD+C,CAA/C,CATqC,CAAvC,CADiD,EAcnDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2D,CAC3DH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD4C,EAE5CP,EAAE,CACA,KADA,EAEA;IAAEE,GAAG,EAAE,sBAAP;IAA+BC,WAAW,EAAE;EAA5C,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACqD,eAAX,EAA4B,UAAUvC,IAAV,EAAgBC,KAAhB,EAAuB;IACjD,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAgD,CAChDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACwC,QAAZ,CAAP,CADgD,CAAhD,CADuD,EAIzDrD,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACyC,OAAZ,CAAP,CADmD,CAAnD,CAJuD,EAOzDtD,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC0C,QAAZ,CAAP,CADoD,CAApD,CAPuD,EAUzDvD,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC2C,MAAZ,CAAP,CADmD,CAAnD,CAVuD,EAazDxD,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsD,CACtDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC4C,MAAZ,IAAsB,GAA7B,CADsD,CAAtD,CAbuD,EAgBzDzD,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAmD,CACnDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC6C,QAAZ,CAAP,CADmD,CAAnD,CAhBuD,EAmBzD1D,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAoD,CACpDJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAAC8C,MAAZ,CAAP,CADoD,CAApD,CAnBuD,CAAlD,CAAT;EAuBD,CAxBD,CAHA,EA4BA,CA5BA,CAF0C,CAA5C,CADyD,CAA3D,CAdiD,CAAnD,CAhBuC,CAAzC,CArKyC,EAwO3C3D,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAmC,CACnCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CACA,MADA,EAEA;IACEG,WAAW,EAAE,kBADf;IAEEK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAAC6D;IAAb;EAFN,CAFA,EAMA,CAAC7D,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CANA,CADqC,EASvCJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CATqC,CAAvC,CADiC,EAYnCJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA2C,CAC3CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAD4C,CAA5C,CADyC,EAI3CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD4C,EAE5CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8D,UAAJ,CAAeC,YAAf,CAA4BC,KAAnC,CAAP,CADS,CAAT,CAF0C,EAK5ChE,GAAG,CAACK,EAAJ,CAAO,GAAP,CAL4C,CAA5C,CAJyC,EAW3CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD6C,EAE7CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8D,UAAJ,CAAeC,YAAf,CAA4BE,OAAnC,CAAP,CADS,CAAT,CAF2C,EAK7CjE,GAAG,CAACK,EAAJ,CAAO,IAAP,CAL6C,CAA7C,CAXyC,CAA3C,CADgD,EAoBlDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CACzCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8D,UAAJ,CAAeI,KAAtB,CAAP,CADyC,CAAzC,CAD2C,EAI7CjE,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAAxC,CAJ2C,CAA7C,CADuC,CAAzC,CApBgD,EA4BlDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA4C,CAC5CH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAD6C,CAA7C,CAD0C,EAI5CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA6C,CAC7CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD6C,EAE7CJ,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8D,UAAJ,CAAeK,UAAf,CAA0BH,KAAjC,CAAP,CAAD,CAAT,CAF2C,EAG7ChE,GAAG,CAACK,EAAJ,CAAO,GAAP,CAH6C,CAA7C,CAJ0C,EAS5CJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA8C,CAC9CJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAD8C,EAE9CJ,EAAE,CAAC,MAAD,EAAS,CACTD,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAON,GAAG,CAAC8D,UAAJ,CAAeK,UAAf,CAA0BF,OAAjC,CAAP,CADS,CAAT,CAF4C,EAK9CjE,GAAG,CAACK,EAAJ,CAAO,IAAP,CAL8C,CAA9C,CAT0C,CAA5C,CA5BgD,CAAlD,CADyC,CAA3C,CAZiC,CAAnC,CADsC,EA8DxCJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAkD,CAClDJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CADkD,EAElDP,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0D,CAC1DH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACoE,wBAAX,EAAqC,UAAUtD,IAAV,EAAgBC,KAAhB,EAAuB;IAC1D,OAAOd,EAAE,CACP,KADO,EAEP;MACEe,GAAG,EAAED,KADP;MAEEX,WAAW,EAAE,eAFf;MAGEwC,KAAK,EAAE5C,GAAG,CAACqE,YAAJ,CAAiBvD,IAAI,CAACkB,IAAtB;IAHT,CAFO,EAOP,CACE/B,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA2C,CAC3CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACU,KAAZ,CAAP,CAD2C,CAA3C,CADJ,EAIEvB,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAA0C,CAC1CJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACkB,IAAZ,CAAP,CAD0C,CAA1C,CAJJ,CAPO,CAAT;EAgBD,CAjBD,CAHA,EAqBA,CArBA,CADwD,CAA1D,CAFgD,CAAlD,CA9DsC,EA0FxC/B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CACxCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CACvCH,EAAE,CACA,MADA,EAEA;IACEG,WAAW,EAAE,kBADf;IAEEK,EAAE,EAAE;MAAEC,KAAK,EAAEV,GAAG,CAACsE;IAAb;EAFN,CAFA,EAMA,CAACtE,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CANA,CADqC,EASvCJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,CATqC,CAAvC,CADsC,EAYxCH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAgD,CAChDH,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACuE,eAAJ,CAAoBC,UAA3B,EAAuC,UAAU1D,IAAV,EAAgBC,KAAhB,EAAuB;IAC5D,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAkD,CACzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,CADuD,EAEzDH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,EAAsC,CACtCH,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACkB,IAAZ,CAAP,CADuC,CAAvC,CADoC,EAItC/B,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAwC,CACxCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACU,KAAZ,CAAP,CADwC,CAAxC,CAJoC,EAOtCvB,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAuC,CACvCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOQ,IAAI,CAACgB,IAAZ,CAAP,CADuC,CAAvC,CAPoC,CAAtC,CAFuD,CAAlD,CAAT;EAcD,CAfD,CAHA,EAmBA,CAnBA,CAD8C,EAsBhD7B,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA+C,CAC/CJ,GAAG,CAACQ,EAAJ,CAAO,CAAP,CAD+C,EAE/CP,EAAE,CACA,KADA,EAEA;IAAEG,WAAW,EAAE;EAAf,CAFA,EAGAJ,GAAG,CAACY,EAAJ,CAAOZ,GAAG,CAACuE,eAAJ,CAAoBE,SAA3B,EAAsC,UAAUC,KAAV,EAAiB3D,KAAjB,EAAwB;IAC5D,OAAOd,EAAE,CAAC,KAAD,EAAQ;MAAEe,GAAG,EAAED,KAAP;MAAcX,WAAW,EAAE;IAA3B,CAAR,EAAmD,CAC1DH,EAAE,CAAC,KAAD,EAAQ;MAAEG,WAAW,EAAE;IAAf,CAAR,CADwD,EAE1DH,EAAE,CAAC,MAAD,EAAS;MAAEG,WAAW,EAAE;IAAf,CAAT,EAAwC,CACxCJ,GAAG,CAACK,EAAJ,CAAOL,GAAG,CAACM,EAAJ,CAAOoE,KAAK,CAAC7B,KAAb,CAAP,CADwC,CAAxC,CAFwD,CAAnD,CAAT;EAMD,CAPD,CAHA,EAWA,CAXA,CAF6C,CAA/C,CAtB8C,CAAhD,CAZsC,CAAxC,CA1FsC,CAAxC,CAxOyC,CAA3C,CAX8D,CAAzD,CAAT;AAoYD,CAvYD;;AAwYA,IAAI8B,eAAe,GAAG,CACpB,YAAY;EACV,IAAI3E,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA0C,CACjDH,EAAE,CAAC,KAAD,EAAQ;IACR2E,WAAW,EAAE;MAAEC,MAAM,EAAE;IAAV,CADL;IAER5D,KAAK,EAAE;MACLC,GAAG,EAAE4D,OAAO,CAAC,gDAAD,CADP;MAEL1D,GAAG,EAAE;IAFA;EAFC,CAAR,CAD+C,CAA1C,CAAT;AASD,CAbmB,EAcpB,YAAY;EACV,IAAIpB,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAgD,CACvDJ,GAAG,CAACK,EAAJ,CAAO,SAAP,CADuD,EAEvDJ,EAAE,CAAC,MAAD,EAAS,CAACD,GAAG,CAACK,EAAJ,CAAO,KAAP,CAAD,CAAT,CAFqD,EAGvDL,GAAG,CAACK,EAAJ,CAAO,GAAP,CAHuD,CAAhD,CAAT;AAKD,CAtBmB,EAuBpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CA9BmB,EA+BpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAyC,CAChDH,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAAxC,CAD8C,EAEhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAF8C,EAGhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAH8C,EAIhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAJ8C,EAKhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAL8C,EAMhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAN8C,EAOhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAwC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAxC,CAP8C,EAQhDJ,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,CAR8C,CAAzC,CAAT;AAUD,CA5CmB,EA6CpB,YAAY;EACV,IAAIJ,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAAuC,CAC9CH,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA8C,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAA9C,CAD4C,EAE9CJ,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAA+C,CAACJ,GAAG,CAACK,EAAJ,CAAO,IAAP,CAAD,CAA/C,CAF4C,CAAvC,CAAT;AAID,CApDmB,EAqDpB,YAAY;EACV,IAAIL,GAAG,GAAG,IAAV;EAAA,IACEC,EAAE,GAAGD,GAAG,CAACE,KAAJ,CAAUD,EADjB;;EAEA,OAAOA,EAAE,CAAC,KAAD,EAAQ;IAAEG,WAAW,EAAE;EAAf,CAAR,EAA8C,CACrDH,EAAE,CAAC,KAAD,EAAQ;IACRG,WAAW,EAAE,UADL;IAERa,KAAK,EAAE;MACLC,GAAG,EAAE4D,OAAO,CAAC,0CAAD,CADP;MAEL1D,GAAG,EAAE;IAFA;EAFC,CAAR,CADmD,EAQrDnB,EAAE,CAAC,MAAD,EAAS;IAAEG,WAAW,EAAE;EAAf,CAAT,EAAuC,CAACJ,GAAG,CAACK,EAAJ,CAAO,MAAP,CAAD,CAAvC,CARmD,CAA9C,CAAT;AAUD,CAlEmB,CAAtB;AAoEAN,MAAM,CAACgF,aAAP,GAAuB,IAAvB;AAEA,SAAShF,MAAT,EAAiB4E,eAAjB"}]}