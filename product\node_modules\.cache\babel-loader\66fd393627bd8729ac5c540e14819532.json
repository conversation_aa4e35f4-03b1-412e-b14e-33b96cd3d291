{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\committeeStatistics\\committeeStatisticsBox.vue", "mtime": 1758775922726}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AA2HA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;EACAA,iBADA;EAEAC;IACAC,cADA;IAEAC,QAFA;IAGAC,UAHA;IAIAC,QAJA;IAKAC,kBALA;IAMAC;EANA,CAFA;;EAUAC;IACA;MACAC,eADA;MAEA;MACAC,sBAHA;MAIAC,kBAJA;MAKAC,wBALA;MAMAC;QACAC,oBADA;QAEAC;MAFA,CANA;MAUAC,WACA;QACAhB,UADA;QAEAiB,eAFA;QAGAH,WACA;UAAAd;UAAAiB;QAAA,CADA,EAEA;UAAAjB;UAAAiB;QAAA,CAFA,EAGA;UAAAjB;UAAAiB;QAAA,CAHA,EAIA;UAAAjB;UAAAiB;QAAA,CAJA,EAKA;UAAAjB;UAAAiB;QAAA,CALA,EAMA;UAAAjB;UAAAiB;QAAA,CANA,EAOA;UAAAjB;UAAAiB;QAAA,CAPA,EAQA;UAAAjB;UAAAiB;QAAA,CARA,EASA;UAAAjB;UAAAiB;QAAA,CATA,EAUA;UAAAjB;UAAAiB;QAAA,CAVA;MAHA,CADA,CAVA;MA4BAC,iBA5BA;MA6BAC,yBA7BA;MA8BAC,OA9BA;MA+BAC,QA/BA;MAgCAC,iBAhCA;MAiCA;MACAC,iBAlCA;MAmCA;MACAC,aApCA;MAqCA;MACAC,qBACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MAlCA,CAtCA;MA0EA;MACAC,uBA3EA;MA4EA;MACAC,gBA7EA;MA8EAC;IA9EA;EAgFA,CA3FA;;EA4FAC,YA5FA;;EA8FAC;IACA;IACA;IACA;IACA;EACA,CAnGA;;EAoGAC;IACA;MACAC;IACA;EACA,CAxGA;;EAyGAC;IACAC;MACA;QAAAC;QAAAC;MAAA;MACAD;MACAC;IACA,CALA;;IAMAC;MACA;MACA;QACAC,eADA;QAEAC,gBAFA;QAGAC,cAHA;QAIAC,eAJA;QAKAC,iBALA;QAMAC;MANA;IAQA,CAhBA;;IAiBAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA,CAzBA;;IA0BA;IACA;MACA;MACA;MACA;IACA,CA/BA;;IAgCA;IACA;MACA;QAAAC;MAAA;MACA;MACA;MACA;IACA,CAtCA;;IAuCA;IACA;MACA;QAAAA;MAAA;MACA;IACA,CA3CA;;IA4CA;IACA;MACA;QAAAA;MAAA;MACA;MACAC;QACA;MACA,CAFA,EAEA,GAFA;IAGA,CAnDA;;IAoDA;IACA;MACA;QAAAD;MAAA;MACA;IACA,CAxDA;;IAyDA;IACA;MACA;QAAAA;MAAA;MACA;IACA,CA7DA;;IA8DA;IACA;MACA;QAAAA;MAAA;MACA;IACA,CAlEA;;IAmEA;IACAE;MACA;QAAAC;MAAA;IACA,CAtEA;;IAuEA;IACAC;MACA;MACA;MACA;MACA,6BAJA,CAKA;;MACAC;IACA;;EA/EA;AAzGA", "names": ["name", "components", "BarScrollChart", "<PERSON><PERSON><PERSON>", "PieChart3D", "<PERSON><PERSON><PERSON>", "HorizontalBarChart", "GenderRatioChart", "data", "currentTime", "showAreaPopover", "<PERSON><PERSON><PERSON>", "selectedDistrictCode", "treeProps", "children", "label", "treeData", "code", "memberTotalNum", "standingMemberTotalNum", "male", "woman", "genderShow", "educationData", "partyData", "sectorAnalysisData", "discussionGroupData", "ageChartData", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "computed", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "initScreen", "calcRate", "windowDraw", "updateTime", "year", "month", "day", "hour", "minute", "second", "getData", "type", "setTimeout", "goHome", "path", "handleNodeClick", "console"], "sourceRoot": "src/views/smartBrainLargeScreen/committeeStatistics", "sources": ["committeeStatisticsBox.vue"], "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <el-popover placement=\"bottom\" width=\"280\" trigger=\"click\" popper-class=\"area-popover\"\r\n            v-model=\"showAreaPopover\">\r\n            <el-scrollbar class=\"region-tree\">\r\n              <el-tree :data=\"treeData\" :props=\"treeProps\" node-key=\"code\" :default-expanded-keys=\"['qingdao']\"\r\n                :current-node-key=\"selectedDistrictCode\" @node-click=\"handleNodeClick\" class=\"area-tree\">\r\n              </el-tree>\r\n            </el-scrollbar>\r\n            <div class=\"header-btn area-select-btn\" slot=\"reference\">\r\n              <span>{{ selectedArea }}</span>\r\n              <i class=\"dropdown-icon\" :class=\"{ 'active': showAreaPopover }\">▼</i>\r\n            </div>\r\n          </el-popover>\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>委员统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 委员数量 -->\r\n        <div class=\"committee-count-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">委员数量</span>\r\n          </div>\r\n          <div class=\"count-content\">\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #02FBFB;\">{{ memberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_member.png\" class=\"count-img\">\r\n              <div class=\"count-label\">委员总数</div>\r\n            </div>\r\n            <div class=\"count-item\">\r\n              <div class=\"count-value\" style=\"color: #F5E74F;\">{{ standingMemberTotalNum }}</div>\r\n              <img src=\"../../../assets/largeScreen/icon_standingMember.png\" class=\"count-img\">\r\n              <div class=\"count-label\">政协常委</div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 性别比例 -->\r\n        <div class=\"gender-ratio-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">性别比例</span>\r\n          </div>\r\n          <div class=\"gender-content\">\r\n            <GenderRatioChart id=\"gender-ratio\" v-if=\"genderShow\" :male-ratio=\"male\" :female-ratio=\"woman\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 年龄 -->\r\n        <div class=\"age-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年龄</span>\r\n          </div>\r\n          <div class=\"age-content\">\r\n            <PieChart id=\"age\" v-if=\"ageChartData.length > 0\" :chart-data=\"ageChartData\" :name=\"ageChartName\"\r\n              legendIcon=\"circle\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 学历 -->\r\n        <div class=\"education-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">学历</span>\r\n          </div>\r\n          <div class=\"education-content\">\r\n            <HorizontalBarChart id=\"education-chart\" :chart-data=\"educationData\" :max-segments=\"30\"\r\n              bar-color=\"#00D4FF\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 党派分布 -->\r\n        <div class=\"party-distribution-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">党派分布</span>\r\n          </div>\r\n          <div class=\"party-content\">\r\n            <PieChart3D id=\"partyDistributionChart\" v-if=\"partyData.length > 0\" :chart-data=\"partyData\" />\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 讨论组人员统计 -->\r\n        <div class=\"discussion-stats-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">讨论组人员统计</span>\r\n          </div>\r\n          <div class=\"discussion-content\">\r\n            <BarChart id=\"discussionGroupChart\" :chart-data=\"discussionGroupData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 界别分析 -->\r\n        <div class=\"sector-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">界别分布</span>\r\n          </div>\r\n          <div class=\"sector-content\">\r\n            <BarScrollChart id=\"sectorAnalysis\" :showCount=\"30\" :chart-data=\"sectorAnalysisData\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\nimport BarChart from '../components/BarChart.vue'\r\nimport PieChart3D from '../components/PieChart3D.vue'\r\nimport PieChart from '../components/PieChart.vue'\r\nimport HorizontalBarChart from '../components/HorizontalBarChart.vue'\r\nimport GenderRatioChart from '../components/GenderRatioChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart,\r\n    BarChart,\r\n    PieChart3D,\r\n    PieChart,\r\n    HorizontalBarChart,\r\n    GenderRatioChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 地区选择相关\r\n      showAreaPopover: false,\r\n      selectedArea: '青岛',\r\n      selectedDistrictCode: '',\r\n      treeProps: {\r\n        children: 'children',\r\n        label: 'name'\r\n      },\r\n      treeData: [\r\n        {\r\n          name: '青岛',\r\n          code: 'qingdao',\r\n          children: [\r\n            { name: '市南区', code: 'shinan' },\r\n            { name: '市北区', code: 'shibei' },\r\n            { name: '李沧区', code: 'licang' },\r\n            { name: '崂山区', code: 'laoshan' },\r\n            { name: '城阳区', code: 'chengyang' },\r\n            { name: '即墨区', code: 'jimo' },\r\n            { name: '胶州市', code: 'jiaozhou' },\r\n            { name: '平度市', code: 'pingdu' },\r\n            { name: '莱西市', code: 'laixi' },\r\n            { name: '西海岸新区', code: 'xihaian' }\r\n          ]\r\n        }\r\n      ],\r\n      memberTotalNum: 0,\r\n      standingMemberTotalNum: 0,\r\n      male: 0,\r\n      woman: 0,\r\n      genderShow: false,\r\n      // 学历数据\r\n      educationData: [],\r\n      // 党派数据\r\n      partyData: [],\r\n      // 界别分析数据\r\n      sectorAnalysisData: [\r\n        // { name: '经济界', value: 32 },\r\n        // { name: '教育界', value: 15 },\r\n        // { name: '科技界', value: 14 },\r\n        // { name: '工商界', value: 13 },\r\n        // { name: '医药卫生界', value: 12 },\r\n        // { name: '社会科学界', value: 10 },\r\n        // { name: '工会', value: 8 },\r\n        // { name: '共青团', value: 7 },\r\n        // { name: '妇联', value: 6 },\r\n        // { name: '科协', value: 5 },\r\n        // { name: '台联', value: 7 },\r\n        // { name: '侨联', value: 3 },\r\n        // { name: '文化艺术界', value: 24 },\r\n        // { name: '体育界', value: 16 },\r\n        // { name: '少数民族界', value: 20 },\r\n        // { name: '宗教界', value: 27 },\r\n        // { name: '特邀人士', value: 21 },\r\n        // { name: '港澳台侨', value: 5 },\r\n        // { name: '对外友好界', value: 19 },\r\n        // { name: '社会福利和社会保障界', value: 12 },\r\n        // { name: '社会治理和社会组织界', value: 21 },\r\n        // { name: '医药卫生界', value: 12 },\r\n        // { name: '社会科学界', value: 10 },\r\n        // { name: '工会', value: 8 },\r\n        // { name: '共青团', value: 7 },\r\n        // { name: '妇联', value: 6 },\r\n        // { name: '科协', value: 5 },\r\n        // { name: '台联', value: 7 },\r\n        // { name: '体育界', value: 16 },\r\n        // { name: '少数民族界', value: 20 },\r\n        // { name: '宗教界', value: 27 },\r\n        // { name: '特邀人士', value: 21 },\r\n        // { name: '港澳台侨', value: 5 },\r\n        // { name: '对外友好界', value: 19 }\r\n      ],\r\n      // 讨论组人员统计数据\r\n      discussionGroupData: [],\r\n      // 年龄数据\r\n      ageChartData: [],\r\n      ageChartName: '年龄占比'\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n    this.getData()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getData () {\r\n      this.getCommitteeMembersNumber()\r\n      this.getCommitteeMembersSex()\r\n      this.getCommitteeMembersAge()\r\n      this.getCommitteeMembersEducation()\r\n      this.getCommitteeMembersParty()\r\n      this.getCommitteeMembersGroup()\r\n      this.getCommitteeMembersSector()\r\n    },\r\n    // 获取委员数量\r\n    async getCommitteeMembersNumber () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberPartyStats()\r\n      this.memberTotalNum = res.data.totalCount\r\n      this.standingMemberTotalNum = res.data.routineCount\r\n    },\r\n    // 获取性别\r\n    async getCommitteeMembersSex () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'gender' })\r\n      this.male = Number(res.data[1].ratio)\r\n      this.woman = Number(res.data[0].ratio)\r\n      this.genderShow = true\r\n    },\r\n    // 获取年龄\r\n    async getCommitteeMembersAge () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'age' })\r\n      this.ageChartData = res.data\r\n    },\r\n    // 获取学历\r\n    async getCommitteeMembersEducation () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'education' })\r\n      this.educationData = res.data\r\n      setTimeout(() => {\r\n        this.startAutoScroll()\r\n      }, 800)\r\n    },\r\n    // 获取党派\r\n    async getCommitteeMembersParty () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'party' })\r\n      this.partyData = res.data\r\n    },\r\n    // 获取讨论组人员统计\r\n    async getCommitteeMembersGroup () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'group' })\r\n      this.discussionGroupData = res.data\r\n    },\r\n    // 获取界别分布\r\n    async getCommitteeMembersSector () {\r\n      const res = await this.$api.smartBrainLargeScreen.memberAnalysis({ type: 'sector' })\r\n      this.sectorAnalysisData = res.data\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    },\r\n    // 处理树节点点击\r\n    handleNodeClick (data, node) {\r\n      // 允许选择所有节点（包括父级青岛）\r\n      this.selectedArea = data.name\r\n      this.selectedDistrictCode = data.code\r\n      this.showAreaPopover = false\r\n      // 这里可以添加切换地区后的数据更新逻辑\r\n      console.log('选择了地区:', data)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 465px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 委员数量\r\n      .committee-count-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .count-content {\r\n          display: flex;\r\n          justify-content: space-around;\r\n          align-items: center;\r\n          height: 100%;\r\n          margin-top: 30px;\r\n\r\n          .count-item {\r\n            text-align: center;\r\n\r\n            .count-value {\r\n              font-weight: 500;\r\n              font-size: 32px;\r\n            }\r\n\r\n            .count-img {\r\n              width: 118px;\r\n              margin-top: -10px;\r\n              margin-bottom: 10px;\r\n            }\r\n\r\n            .count-label {\r\n              font-size: 16px;\r\n              color: #FFFFFF;\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 性别比例\r\n      .gender-ratio-section {\r\n        background: url('../../../assets/largeScreen/gender_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .gender-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 年龄\r\n      .age-section {\r\n        background: url('../../../assets/largeScreen/age_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 3; // 第三列\r\n        grid-row: 1; // 第一行\r\n\r\n        .age-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 学历\r\n      .education-section {\r\n        background: url('../../../assets/largeScreen/education_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 2; // 第二行\r\n\r\n        .education-content {\r\n          margin-top: 50px;\r\n          height: 220px;\r\n        }\r\n      }\r\n\r\n      // 党派分布\r\n      .party-distribution-section {\r\n        background: url('../../../assets/largeScreen/party_distribution_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2 / 4; // 跨越第2和第3列（在第二行）\r\n        grid-row: 2; // 明确指定在第二行\r\n\r\n        .party-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n\r\n      // 讨论人员统计\r\n      .discussion-stats-section {\r\n        background: url('../../../assets/largeScreen/discussion_stats_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        grid-column: 1 / -1; // 跨越三列（讨论组人员统计在第三行）\r\n        grid-row: 3; // 明确指定在第三行\r\n\r\n        .discussion-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 界别分析\r\n      .sector-analysis-section {\r\n        background: url('../../../assets/largeScreen/sector_analysis_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .sector-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 25px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// el-popover 自定义样式\r\n.area-popover {\r\n  width: 290px !important;\r\n  padding: 0 !important;\r\n\r\n  .region-tree {\r\n    width: 100%;\r\n\r\n    ::v-deep .el-tree-node {\r\n      .el-tree-node__content {\r\n        height: 40px;\r\n        line-height: 40px;\r\n\r\n        .el-tree-node__label {\r\n          font-size: 14px;\r\n          font-weight: 400;\r\n        }\r\n      }\r\n\r\n      // 当前选中节点\r\n      &.is-current>.el-tree-node__content {\r\n        background: #ebeef9;\r\n        color: #3657C0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}