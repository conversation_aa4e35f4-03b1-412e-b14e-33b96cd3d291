{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\home\\homeBox.vue", "mtime": 1758767185562}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["homeBox.vue"], "names": [], "mappings": ";AA6MA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "homeBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/home", "sourcesContent": ["<template>\n  <div class=\"big-screen\" ref=\"bigScreen\">\n    <div class=\"screen-header\">\n      <div class=\"header-left\">\n        <span class=\"date-time\">{{ currentTime }}</span>\n        <span class=\"weather\">晴 24℃ 东南风</span>\n      </div>\n      <div class=\"header-center\">\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\n      </div>\n      <div class=\"header-right\"></div>\n    </div>\n    <div class=\"screen-content\">\n      <div class=\"left-panel\">\n        <!-- 委员统计 -->\n        <div class=\"committee_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleCommitteeClick\">委员统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"committee_statistics_content\">\n            <div class=\"committee_statistics_num\">\n              <div v-for=\"(item, index) in committeeStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}</div>\n                </div>\n              </div>\n            </div>\n            <div class=\"committee_statistics_chart\">\n              <BarScrollChart id=\"committee-statistics\" :showCount=\"5\" :chart-data=\"committeeBarData\" />\n            </div>\n          </div>\n        </div>\n        <!-- 提案统计 -->\n        <div class=\"proposal_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleProposalClick\">提案统计</span>\n            <span class=\"header_text_right\">十二届二次会议</span>\n            <span class=\"header_text_center\">提交提案总数：<span>873</span>件</span>\n          </div>\n          <div class=\"proposal_statistics_content\">\n            <div class=\"proposal_statistics_num\">\n              <div v-for=\"(item, index) in proposalStatisticsNum\" :key=\"index\" class=\"num_box\">\n                <img :src=\"item.icon\" alt=\"\" class=\"num_icon\">\n                <div>\n                  <div class=\"num_label\">{{ item.label }}</div>\n                  <div class=\"num_value\" :style=\"`color:${item.color}`\">{{ item.value }}<span class=\"num_unit\">{{\n                    item.unit }}</span></div>\n                </div>\n              </div>\n            </div>\n            <div class=\"proposal_statistics_chart\">\n              <PieChart id=\"proposal-statistics\" :chart-data=\"proposalChartData\" :name=\"proposalChartName\"\n                legendIcon=\"circle\" :legendItemGap=\"12\" :radius=\"['60%', '85%']\" :center=\"['22%', '50%']\"\n                :lineRadius=\"['94%', '95%']\" :lineCenter=\"['22%', '50%']\" graphicLeft=\"12%\" graphicTop=\"23%\"\n                :graphicShapeR=\"40\" />\n            </div>\n          </div>\n        </div>\n        <!-- 工作动态 -->\n        <div class=\"work_dynamics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">工作动态</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"work_dynamics_content\">\n            <div class=\"dynamics-list\" ref=\"dynamicsList\">\n              <div v-for=\"(item, index) in workDynamicsData\" :key=\"item.id\" class=\"dynamics-item\"\n                :class=\"{ 'with-bg-image': index % 2 === 0, 'with-bg-color': index % 2 === 1 }\">\n                <div class=\"dynamics-content\">\n                  <div class=\"dynamics-title\">{{ item.title }}</div>\n                  <div class=\"dynamics-date\">{{ item.publishDate }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"center-panel\">\n        <!-- 地图 -->\n        <div class=\"map_box\">\n          <MapComponent :data=\"mapData\" :areaId=\"areaId + ''\" :areaName=\"areaName\" @region-click=\"handleRegionClick\" />\n        </div>\n        <!-- 履职统计 -->\n        <div class=\"performance_statistics\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePerformanceClick\">履职统计</span>\n            <span class=\"header_text_right\">十二届二次</span>\n          </div>\n          <div class=\"performance_statistics_content\">\n            <div class=\"table-container\">\n              <!-- 固定表头 -->\n              <div class=\"table-header\">\n                <div class=\"header-cell\">姓名</div>\n                <div class=\"header-cell\">会议活动</div>\n                <div class=\"header-cell\">政协提案</div>\n                <div class=\"header-cell\">社情民意</div>\n                <div class=\"header-cell\">议政建言</div>\n                <div class=\"header-cell\">读书心得</div>\n                <div class=\"header-cell\">委员培训</div>\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\n              </div>\n              <!-- 可滚动内容 -->\n              <div class=\"table-body\" ref=\"performanceTableBody\">\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\n                  <div class=\"table-cell name-col\">{{ item.userName }}</div>\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\n                  <div class=\"table-cell opinion-col\">{{ item.social }}</div>\n                  <div class=\"table-cell suggestion-col\">{{ item.advise }}\n                  </div>\n                  <div class=\"table-cell reading-col\">{{ item.activity }}</div>\n                  <div class=\"table-cell training-col\">{{ item.others }}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"right-panel\">\n        <!-- 社情民意 -->\n        <div class=\"social\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handlePublicOpinionClick\">社情民意</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"social_content\">\n            <div class=\"social-data-container\">\n              <div class=\"left-data-item\">\n                <div class=\"left-data-label\">委员报送</div>\n                <div class=\"left-data-value\">总数<span>{{ socialData.memberSubmit.count }}</span>篇</div>\n                <div class=\"left-data-detail\">采用<span>{{ socialData.memberSubmit.adopted }}</span> 篇</div>\n              </div>\n              <div class=\"center-chart\">\n                <div class=\"progress-content\">\n                  <div class=\"total-number\">{{ socialData.total }}</div>\n                  <div class=\"total-label\">总数</div>\n                </div>\n              </div>\n              <div class=\"right-data-item\">\n                <div class=\"right-data-label\">单位报送</div>\n                <div class=\"right-data-value\">总数<span>{{ socialData.unitSubmit.count }}</span>篇</div>\n                <div class=\"right-data-detail\">采用<span>{{ socialData.unitSubmit.adopted }}</span> 篇</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 会议活动 -->\n        <div class=\"conference_activities\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\">会议活动</span>\n            <span class=\"header_text_right\">本年</span>\n          </div>\n          <div class=\"conference_activities_content\">\n            <div class=\"activities-grid\">\n              <div v-for=\"(item, index) in conferenceActivitiesData\" :key=\"index\" class=\"activity-item\"\n                :class=\"getItemClass(item.name)\">\n                <div class=\"activity-value\">{{ item.value }}</div>\n                <div class=\"activity-name\">{{ item.name }}</div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <!-- 网络议政 -->\n        <div class=\"discussions\">\n          <div class=\"header_box\">\n            <span class=\"header_text_left\" @click=\"handleNetWorkClick\">网络议政</span>\n            <span class=\"header_text_right\"></span>\n          </div>\n          <div class=\"discussions_content\">\n            <!-- 统计数据区域 -->\n            <div class=\"statistics-section\">\n              <div v-for=\"(item, index) in discussionsData.statistics\" :key=\"index\" class=\"stat-item\">\n                <div class=\"stat-dot\"></div>\n                <div class=\"stat-info\">\n                  <span class=\"stat-name\">{{ item.name }}</span>\n                  <span class=\"stat-value\">{{ item.value }}</span>\n                  <span class=\"stat-unit\">{{ item.unit }}</span>\n                </div>\n              </div>\n            </div>\n\n            <!-- 最热话题区域 -->\n            <div class=\"hot-topics-section\">\n              <div class=\"hot-topics-header\">\n                <img src=\"../../../assets/largeScreen/icon_hot.png\" alt=\"热门\" class=\"hot-icon\">\n                <span class=\"hot-title\">最热话题</span>\n              </div>\n              <div class=\"topics-list\">\n                <div v-for=\"(topic, index) in discussionsData.hotTopics\" :key=\"index\" class=\"topic-item\">\n                  <div class=\"topic-dot\"></div>\n                  <span class=\"topic-text\">{{ topic.title }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport { useIndex } from '../screen.js'\nimport MapComponent from '../components/MapComponent.vue'\nimport PieChart from '../components/PieChart.vue'\nimport BarScrollChart from '../components/BarScrollChart.vue'\n\nexport default {\n  name: 'BigScreen',\n  components: {\n    MapComponent,\n    PieChart,\n    BarScrollChart\n  },\n  data () {\n    return {\n      currentTime: '',\n      // 委员统计\n      committeeStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_cppcc_member.png'), label: '政协委员（人）', value: 0, color: '#ffffff' },\n        { icon: require('../../../assets/largeScreen/icon_committee_member.png'), label: '政协常委', value: 0, color: '#FCD603' }\n      ],\n      // 委员统计柱状图数据\n      committeeBarData: [],\n      // 提案统计\n      proposalStatisticsNum: [\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '委员提案', value: '456', unit: '件', color: '#0DBCDB' },\n        { icon: require('../../../assets/largeScreen/icon_circles_proposal.png'), label: '界别提案', value: '354', unit: '件', color: '#0058FF' },\n        { icon: require('../../../assets/largeScreen/icon_committee_proposal.png'), label: '组织提案', value: '211', unit: '件', color: '#1AEBDD' }\n      ],\n      // 提案统计图表数据\n      proposalChartData: [\n        { name: '政府制约', value: 22.52 },\n        { name: '县区市政', value: 18.33 },\n        { name: '司法法治', value: 15.73 },\n        { name: '区市政府', value: 11.34 },\n        { name: '科技工商', value: 9.56 },\n        { name: '教育文化', value: 8.09 },\n        { name: '派出机构', value: 4.21 },\n        { name: '社会事业', value: 3.71 },\n        { name: '企事业', value: 3.65 },\n        { name: '农村卫生', value: 3.21 },\n        { name: '其他机构', value: 1.86 },\n        { name: '各群体他', value: 1.02 }\n      ],\n      proposalChartName: '提案统计',\n      // 工作动态数据\n      workDynamicsData: [],\n      // 履职统计数据\n      performanceData: [],\n      // 社情民意数据\n      socialData: {\n        memberSubmit: {\n          count: 0,\n          adopted: 0\n        },\n        unitSubmit: {\n          count: 0,\n          adopted: 0\n        },\n        total: 0\n      },\n      // 会议活动数据\n      conferenceActivitiesData: [\n        { name: '会议次数', value: 0 },\n        { name: '活动次数', value: 0 },\n        { name: '会议人数', value: 0 },\n        { name: '活动人数', value: 0 }\n      ],\n      // 网络议政数据\n      discussionsData: {\n        statistics: [\n          { name: '发布议题', value: 0, unit: '个' },\n          { name: '累计参与人次', value: 0, unit: '次' },\n          { name: '累计征求意见', value: 0, unit: '条' }\n        ],\n        hotTopics: [\n          '推进黄河国家文化公园建设',\n          '持续推进黄河流域生态保护修复，助力\"先行区\"建设',\n          '全面加强新时代中小学劳动教育'\n        ]\n      },\n      mapData: [\n        { name: '市南区', value: '1200', areaId: '370202', adcode: 370202 },\n        { name: '市北区', value: '1300', areaId: '370203', adcode: 370203 },\n        { name: '黄岛区', value: '850', areaId: '370211', adcode: 370211 },\n        { name: '崂山区', value: '700', areaId: '370212', adcode: 370212 },\n        { name: '李沧区', value: '1000', areaId: '370213', adcode: 370213 },\n        { name: '城阳区', value: '1100', areaId: '370214', adcode: 370214 },\n        { name: '即墨区', value: '950', areaId: '370215', adcode: 370215 },\n        { name: '胶州市', value: '800', areaId: '370281', adcode: 370281 },\n        { name: '平度市', value: '1400', areaId: '370283', adcode: 370283 },\n        { name: '莱西市', value: '600', areaId: '370285', adcode: 370285 }\n      ],\n      areaId: JSON.parse(sessionStorage.getItem('areaId' + this.$logo())),\n      areaName: '青岛市',\n      autoScrollInterval: null,\n      performanceScrollInterval: null\n    }\n  },\n  computed: {\n  },\n  mounted () {\n    this.initScreen()\n    this.updateTime()\n    this.timeInterval = setInterval(this.updateTime, 1000)\n    this.getData()\n    this.startAutoScroll()\n    this.startPerformanceAutoScroll()\n  },\n  beforeDestroy () {\n    if (this.timeInterval) {\n      clearInterval(this.timeInterval)\n    }\n    if (this.autoScrollInterval) {\n      clearInterval(this.autoScrollInterval)\n    }\n    if (this.performanceScrollInterval) {\n      clearInterval(this.performanceScrollInterval)\n    }\n  },\n  methods: {\n    initScreen () {\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\n      calcRate()\n      windowDraw()\n    },\n    updateTime () {\n      const now = new Date()\n      this.currentTime = now.toLocaleString('zh-CN', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit',\n        hour: '2-digit',\n        minute: '2-digit',\n        second: '2-digit'\n      })\n    },\n    getData () {\n      this.getCommitteeStatistics()\n      this.getWorkDynamicList()\n      this.dutyStatstics()\n      this.getSocialOpinion()\n      this.getMeetingActivity()\n      this.getNetworkDiscussion()\n      this.getHotTopicsList()\n    },\n    // 获取委员统计\n    async getCommitteeStatistics () {\n      const res = await this.$api.smartBrainLargeScreen.memberPartyStats()\n      this.committeeBarData = res.data.parties.map(item => {\n        return {\n          name: item.label,\n          value: item.count\n        }\n      })\n      this.committeeStatisticsNum[0].value = res.data.routineCount\n      this.committeeStatisticsNum[1].value = res.data.totalCount\n    },\n    // 获取工作动态\n    async getWorkDynamicList () {\n      const res = await this.$api.smartBrainLargeScreen.workDynamicList()\n      this.workDynamicsData = res.data\n    },\n    // 获取履职统计\n    async dutyStatstics () {\n      const res = await this.$api.smartBrainLargeScreen.dutyStatistics()\n      this.performanceData = res.data\n    },\n    // 获取社情民意\n    async getSocialOpinion () {\n      const res = await this.$api.smartBrainLargeScreen.socialStatistics()\n      this.socialData.memberSubmit.count = res.data.memberCount\n      this.socialData.memberSubmit.adopted = res.data.memberAdoptCount\n      this.socialData.unitSubmit.count = res.data.unitCount\n      this.socialData.unitSubmit.adopted = res.data.unitAdoptCount\n      this.socialData.total = res.data.total\n    },\n    // 获取会议活动\n    async getMeetingActivity () {\n      const res = await this.$api.smartBrainLargeScreen.meetingActivity()\n      this.conferenceActivitiesData[0].value = res.data.meetingParticipants\n      this.conferenceActivitiesData[1].value = res.data.activityParticipants\n      this.conferenceActivitiesData[2].value = res.data.meetingCount\n      this.conferenceActivitiesData[3].value = res.data.activityCount\n    },\n    // 获取网络议政\n    async getNetworkDiscussion () {\n      const res = await this.$api.smartBrainLargeScreen.networkPolitics()\n      this.discussionsData.statistics[0].value = res.data.publishedSurveyCount\n      this.discussionsData.statistics[1].value = res.data.totalParticipantCount\n      this.discussionsData.statistics[2].value = res.data.adviceCount\n    },\n    // 获取网络议政最热话题\n    async getHotTopicsList () {\n      const res = await this.$api.smartBrainLargeScreen.hotTopicsList({ pageNo: 1, pageSize: 3 })\n      this.discussionsData.hotTopics = res.data\n    },\n    // 开始工作动态自动滚动\n    startAutoScroll () {\n      // 等待数据加载完成后再启动自动滚动\n      setTimeout(() => {\n        this.autoScrollInterval = setInterval(() => {\n          this.scrollElement(this.$refs.dynamicsList, this.workDynamicsData, 50)\n        }, 3000) // 每3秒滚动一次\n      }, 1000)\n    },\n    // 开始履职统计自动滚动\n    startPerformanceAutoScroll () {\n      // 等待数据加载完成后再启动自动滚动\n      setTimeout(() => {\n        this.performanceScrollInterval = setInterval(() => {\n          this.scrollElement(this.$refs.performanceTableBody, this.performanceData, 36)\n        }, 2000) // 每2秒滚动一次，频率稍快于工作动态\n      }, 1000)\n    },\n    // 通用滚动函数\n    scrollElement (element, dataList, itemHeight) {\n      if (!element || !dataList || dataList.length === 0) return\n      const visibleHeight = element.clientHeight\n      const scrollHeight = element.scrollHeight\n      // 如果内容不足以滚动，则不执行滚动\n      if (scrollHeight <= visibleHeight) return\n      // 获取当前滚动位置\n      const currentScrollTop = element.scrollTop\n      // 计算下一个滚动位置\n      let nextScrollTop = currentScrollTop + itemHeight\n      // 如果滚动到底部，则回到顶部\n      if (nextScrollTop + visibleHeight >= scrollHeight) {\n        nextScrollTop = 0\n      }\n      // 平滑滚动\n      element.scrollTo({\n        top: nextScrollTop,\n        behavior: 'smooth'\n      })\n    },\n    getItemClass (name) {\n      if (name.includes('会议')) {\n        return 'meeting-item'\n      } else if (name.includes('活动')) {\n        return 'activity-item-bg'\n      }\n      return ''\n    },\n    handleRegionClick (region) {\n      console.log('选中地区:', region)\n      // 这里可以添加地区点击后的业务逻辑\n      // 比如显示该地区的详细数据等\n    },\n    // 打开委员统计\n    handleCommitteeClick () {\n      this.$router.push({ path: '/committeeStatisticsBox', query: { route: '/committeeStatisticsBox' } })\n    },\n    // 打开提案统计\n    handleProposalClick () {\n      this.$router.push({ path: '/proposalStatisticsBox', query: { route: '/proposalStatisticsBox' } })\n    },\n    // 打开履职统计\n    handlePerformanceClick () {\n      this.$router.push({ path: '/performanceStatisticsBox', query: { route: '/performanceStatisticsBox' } })\n    },\n    // 打开网络议政\n    handleNetWorkClick () {\n      this.$router.push({ path: '/networkDiscussBox', query: { route: '/networkDiscussBox' } })\n    },\n    // 打开社情民意\n    handlePublicOpinionClick () {\n      this.$router.push({ path: '/publicOpinionBox', query: { route: '/publicOpinionBox' } })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.big-screen {\n  width: 1920px;\n  height: 1080px;\n  position: relative;\n  top: 50%;\n  left: 50%;\n  transform: translate(-50%, -50%);\n  transform-origin: left top;\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\n  background-size: cover;\n  background-position: center;\n\n  .screen-header {\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\n    background-size: 100% 100%;\n    background-position: center;\n    height: 65px;\n    display: flex;\n    align-items: center;\n    padding: 0 40px;\n\n    .header-left {\n      display: flex;\n      gap: 20px;\n      font-size: 14px;\n      color: #8cc8ff;\n      flex: 1;\n    }\n\n    .header-center {\n      width: 60%;\n      text-align: center;\n    }\n\n    .header-right {\n      flex: 1;\n    }\n  }\n\n  .screen-content {\n    height: calc(100% - 65px);\n    display: flex;\n    padding: 35px 20px 0 20px;\n    gap: 30px;\n\n    .header_box {\n      position: absolute;\n      top: 15px;\n      left: 24px;\n      right: 0;\n      display: flex;\n      align-items: center;\n      justify-content: space-between;\n\n      .header_text_left {\n        font-weight: bold;\n        font-size: 20px;\n        color: #FFFFFF;\n        cursor: pointer;\n      }\n\n      .header_text_right {\n        font-size: 15px;\n        color: #FFD600;\n      }\n\n      .header_text_center {\n        font-size: 15px;\n        color: #FFFFFF;\n        display: flex;\n        align-items: center;\n\n        span {\n          font-weight: 500;\n          font-size: 24px;\n          color: #02FBFB;\n          margin: 0 10px 0 6px;\n        }\n      }\n    }\n\n    .left-panel,\n    .right-panel {\n      width: 470px;\n      display: flex;\n      flex-direction: column;\n      gap: 20px 30px;\n    }\n\n    .left-panel {\n      .committee_statistics {\n        background: url('../../../assets/largeScreen/committee_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .committee_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .committee_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-around;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 64px;\n                height: 64px;\n                margin-right: 14px;\n              }\n\n              .num_label {\n                font-size: 15px;\n                color: #B4C0CC;\n                margin-bottom: 14px;\n              }\n\n              .num_value {\n                font-weight: bold;\n                font-size: 26px;\n                color: #FFFFFF;\n              }\n            }\n          }\n\n          .committee_statistics_chart {\n            width: 100%;\n            height: 180px;\n          }\n        }\n      }\n\n      .proposal_statistics {\n        background: url('../../../assets/largeScreen/proposal_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 310px;\n        width: 100%;\n\n        .proposal_statistics_content {\n          height: 100%;\n          margin-top: 72px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .proposal_statistics_num {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .num_box {\n              display: flex;\n              align-items: center;\n\n              .num_icon {\n                width: 54px;\n                height: 54px;\n                margin-right: 10px;\n              }\n\n              .num_label {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 5px;\n              }\n\n              .num_value {\n                font-size: 20px;\n                color: #0DBCDB;\n                font-weight: 500;\n\n                .num_unit {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  font-weight: normal;\n                  margin-left: 4px;\n                }\n              }\n            }\n          }\n\n          .proposal_statistics_chart {\n            height: 150px;\n            margin-top: 20px;\n          }\n        }\n      }\n\n      .work_dynamics {\n        background: url('../../../assets/largeScreen/work_dynamics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .work_dynamics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 14px;\n          margin-right: 14px;\n\n          .dynamics-list {\n            height: calc(100% - 70px);\n            overflow-y: auto;\n\n            &::-webkit-scrollbar {\n              width: 4px;\n            }\n\n            &::-webkit-scrollbar-track {\n              background: rgba(0, 30, 60, 0.3);\n              border-radius: 2px;\n            }\n\n            &::-webkit-scrollbar-thumb {\n              background: rgba(0, 212, 255, 0.4);\n              border-radius: 2px;\n\n              &:hover {\n                background: rgba(0, 212, 255, 0.6);\n              }\n            }\n\n            .dynamics-item {\n              margin-bottom: 12px;\n              overflow: hidden;\n              position: relative;\n\n              &:last-child {\n                margin-bottom: 0;\n              }\n\n              // 奇数项 - 背景图片样式\n              &.with-bg-image {\n                background: url('../../../assets/largeScreen/table_bg.png') no-repeat;\n                background-size: 100% 100%;\n                background-position: center;\n              }\n\n              // 偶数项 - 背景颜色样式\n              &.with-bg-color {\n                background: rgba(6, 79, 219, 0.05);\n              }\n\n              .dynamics-content {\n                padding: 12px 15px;\n                position: relative;\n                z-index: 2;\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n\n                .dynamics-title {\n                  flex: 1;\n                  color: #fff;\n                  font-size: 16px;\n                  margin-right: 16px;\n                  // 文本溢出处理\n                  display: -webkit-box;\n                  -webkit-line-clamp: 1;\n                  -webkit-box-orient: vertical;\n                  overflow: hidden;\n                  text-overflow: ellipsis;\n                }\n\n                .dynamics-date {\n                  flex-shrink: 0;\n                  font-size: 16px;\n                  color: #FFFFFF;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .right-panel {\n      .social {\n        background: url('../../../assets/largeScreen/social_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .social_content {\n          height: 190px;\n          margin-top: 75px;\n          margin-left: 12px;\n          margin-right: 12px;\n          background: url('../../../assets/largeScreen/social_content_bg.png') no-repeat;\n          background-size: 100% 100%;\n          background-position: center;\n\n          .social-data-container {\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n            width: 100%;\n            height: 100%;\n\n            .left-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-right: 20px;\n\n              .left-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .left-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .left-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n\n            .center-chart {\n              flex: 1;\n              display: flex;\n              justify-content: center;\n              align-items: center;\n\n              .progress-content {\n                position: absolute;\n                display: flex;\n                flex-direction: column;\n                align-items: center;\n                justify-content: center;\n\n                .total-number {\n                  font-weight: 500;\n                  font-size: 24px;\n                  color: #FFD600;\n                  margin-bottom: 8px;\n                }\n\n                .total-label {\n                  font-size: 14px;\n                  color: #ffffff;\n                }\n              }\n            }\n\n            .right-data-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              text-align: center;\n              flex: 1;\n              margin-left: 20px;\n\n              .right-data-label {\n                font-size: 14px;\n                color: #19ECFF;\n                margin-bottom: 20px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .right-data-value {\n                font-size: 14px;\n                color: #FFFFFF;\n                margin-bottom: 15px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #FFD600;\n                  margin: 0 5px;\n                }\n              }\n\n              .right-data-detail {\n                font-size: 14px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n\n                span {\n                  font-weight: 400;\n                  font-size: 20px;\n                  color: #19ECFF;\n                  margin: 0 5px;\n                }\n              }\n            }\n          }\n        }\n      }\n\n      .conference_activities {\n        background: url('../../../assets/largeScreen/conference_activities_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 290px;\n        width: 100%;\n\n        .conference_activities_content {\n          margin-top: 70px;\n          margin-left: 20px;\n          margin-right: 20px;\n\n          .activities-grid {\n            display: grid;\n            grid-template-columns: 1fr 1fr;\n            grid-template-rows: 1fr 1fr;\n            gap: 15px;\n            height: 100%;\n\n            .activity-item {\n              display: flex;\n              flex-direction: column;\n              align-items: center;\n              justify-content: center;\n              background-size: 100% 100%;\n              background-repeat: no-repeat;\n              background-position: center;\n              height: 92px;\n\n              .activity-value {\n                font-weight: 500;\n                font-size: 32px;\n                color: #FFFFFF;\n                line-height: 24px;\n                margin-bottom: 12px;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              .activity-name {\n                font-size: 16px;\n                color: #FFFFFF;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n\n              &.meeting-item {\n                background-image: url('../../../assets/largeScreen/icon_meeting_item_bg.png');\n              }\n\n              &.activity-item-bg {\n                background-image: url('../../../assets/largeScreen/icon_activity_item_bg.png');\n              }\n            }\n          }\n        }\n      }\n\n      .discussions {\n        background: url('../../../assets/largeScreen/discussions_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 320px;\n        width: 100%;\n\n        .discussions_content {\n          margin-top: 75px;\n          margin-left: 20px;\n          margin-right: 20px;\n          height: calc(100% - 90px);\n          display: flex;\n          flex-direction: column;\n\n          .statistics-section {\n            display: flex;\n            flex-wrap: wrap;\n            justify-content: space-between;\n            gap: 20px;\n            margin-bottom: 25px;\n\n            .stat-item {\n              display: flex;\n              align-items: center;\n              gap: 12px;\n\n              .stat-dot {\n                width: 10px;\n                height: 10px;\n                border-radius: 50%;\n                background: linear-gradient(180deg, #00EEFF 0%, #E1FDFF 100%);\n                flex-shrink: 0;\n                margin-top: 5px;\n              }\n\n              .stat-info {\n                display: flex;\n                align-items: center;\n                gap: 8px;\n\n                .stat-name {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-value {\n                  font-weight: 500;\n                  font-size: 20px;\n                  color: #FFD600;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n\n                .stat-unit {\n                  font-size: 15px;\n                  color: #FFFFFF;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                }\n              }\n            }\n          }\n\n          .hot-topics-section {\n            flex: 1;\n            background: rgba(31, 198, 255, 0.16);\n            padding: 12px 16px;\n\n            .hot-topics-header {\n              display: flex;\n              align-items: center;\n              gap: 8px;\n              margin-bottom: 15px;\n\n              .hot-icon {\n                width: 20px;\n                height: 20px;\n              }\n\n              .hot-title {\n                font-size: 16px;\n                color: #02FBFB;\n                font-weight: 500;\n                font-family: DIN-BoldItalic, DIN-BoldItalic;\n              }\n            }\n\n            .topics-list {\n              display: flex;\n              flex-direction: column;\n              gap: 12px;\n\n              .topic-item {\n                display: flex;\n                align-items: flex-start;\n                gap: 10px;\n\n                .topic-dot {\n                  width: 6px;\n                  height: 6px;\n                  border-radius: 50%;\n                  background: rgba(217, 217, 217, 0.5);\n                  margin-top: 8px;\n                  flex-shrink: 0;\n                }\n\n                .topic-text {\n                  font-size: 14px;\n                  color: #FFFFFF;\n                  line-height: 22px;\n                  font-family: DIN-BoldItalic, DIN-BoldItalic;\n                  text-overflow: ellipsis;\n                  overflow: hidden;\n                  white-space: nowrap;\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .center-panel {\n      flex: 1;\n      gap: 20px;\n      display: flex;\n      flex-direction: column;\n\n      .map_box {\n        background: url('../../../assets/largeScreen/map_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        height: 650px;\n        width: 100%;\n        position: relative;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n      }\n\n      .performance_statistics {\n        background: url('../../../assets/largeScreen/performance_statistics_bg.png') no-repeat;\n        background-size: 100% 100%;\n        background-position: center;\n        position: relative;\n        height: 270px;\n        width: 100%;\n\n        .performance_statistics_content {\n          height: 100%;\n          margin-top: 65px;\n          margin-left: 16px;\n          margin-right: 16px;\n\n          .table-container {\n            height: calc(100% - 75px);\n            display: flex;\n            flex-direction: column;\n            border: 1px solid rgba(0, 212, 255, 0.2);\n            overflow: hidden;\n            /* 使用CSS Grid确保列对齐 */\n            --name-col-width: 120px;\n            --scrollbar-width: 6px;\n\n            .table-header {\n              display: grid;\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\n              border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n              position: sticky;\n              top: 0;\n              z-index: 10;\n\n              .header-cell {\n                padding: 12px 8px;\n                text-align: center;\n                color: #E6F7FF;\n                font-size: 15px;\n                border-right: 1px solid rgba(0, 212, 255, 0.3);\n                display: flex;\n                align-items: center;\n                justify-content: center;\n\n                &:last-child {\n                  border-right: none;\n                  background: transparent;\n                  border: none;\n                }\n\n                // &.name-col {\n                //   background: rgba(0, 100, 180, 0.9);\n                //   font-weight: 600;\n                // }\n              }\n            }\n\n            .table-body {\n              flex: 1;\n              overflow-y: auto;\n\n              &::-webkit-scrollbar {\n                width: 6px;\n              }\n\n              &::-webkit-scrollbar-track {\n                background: rgba(0, 30, 60, 0.3);\n                border-radius: 3px;\n              }\n\n              &::-webkit-scrollbar-thumb {\n                background: rgba(0, 212, 255, 0.4);\n                border-radius: 3px;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.6);\n                }\n              }\n\n              .table-row {\n                display: grid;\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\n                border-bottom: 1px solid rgba(0, 212, 255, 0.4);\n                transition: all 0.3s ease;\n\n                &:hover {\n                  background: rgba(0, 212, 255, 0.1);\n                }\n\n                .table-cell {\n                  padding: 12px 8px;\n                  text-align: center;\n                  color: #FFFFFF;\n                  font-size: 14px;\n                  border-right: 1px solid rgba(0, 212, 255, 0.4);\n                  transition: all 0.3s ease;\n                  display: flex;\n                  align-items: center;\n                  justify-content: center;\n\n                  &:last-child {\n                    border-right: none;\n                  }\n\n                  &.name-col {\n                    background: rgba(0, 60, 120, 0.4);\n                    color: #FFF;\n                    font-weight: 500;\n                  }\n\n                  &.meeting-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    color: #59F7CA;\n                    font-weight: 500;\n                    font-size: 16px;\n                  }\n\n                  &.proposal-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #00FFF7;\n                  }\n\n                  &.opinion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF386B;\n                  }\n\n                  &.suggestion-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #81C4E4;\n                  }\n\n                  &.reading-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #387BFD;\n                  }\n\n                  &.training-col {\n                    background: rgba(10, 63, 111, 0.4);\n                    font-weight: 500;\n                    font-size: 16px;\n                    color: #FF911F;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}