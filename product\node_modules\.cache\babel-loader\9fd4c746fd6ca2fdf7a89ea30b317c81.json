{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\GenderRatioChart.vue", "mtime": 1758771826392}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\babel.config.js", "mtime": 1660102036886}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "mappings": "AAoBA;AAEA;EACAA,wBADA;EAEAC;IACAC;MACAC,YADA;MAEAC;IAFA,CADA;IAKAC;MACAF,YADA;MAEAG;IAFA,CALA;IASAC;MACAJ,YADA;MAEAG;IAFA;EATA,CAFA;;EAgBAE;IACA;MACAC,eADA;MAEAC;IAFA;EAIA,CArBA;;EAsBAC;IACA;EACA,CAxBA;;EAyBAC;IACA;MACA;IACA;;IACA;MACA;IACA;EACA,CAhCA;;EAiCAC;IACAC;MACA;MACA;IACA,CAJA;;IAMAC;MACA;MACA;MACA,8CAHA,CAIA;;MACA;MACA;MACA;MACA,mBARA,CASA;;MACA;QACA;QACAC;UACAC,QADA;UACA;UACAC;YACAC,gEADA;YAEAC;UAFA;QAFA,GAFA,CASA;;QACAC;UACAJ,QADA;UACA;UACAC;YACAC,oBADA;YAEAC;UAFA;QAFA;MAOA,CA3BA,CA4BA;;;MACA;;MACA;QACAE;QACAA;MACA;;MACA;QACAC,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;QACA;UACAvB,WADA;UAEAwB,qBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,+BADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CAFA,EAiBA;QACA;UACA7B,WADA;UAEAwB,sBAFA;UAGAC,sBAHA;UAIAK,cAJA;UAKAzB,kBALA;UAMAqB;YAAAC;UAAA,CANA;UAOAC;YAAAD;UAAA,CAPA;UAQAE;QARA,CAlBA,EA4BA;QACA;UACA7B,WADA;UAEAwB,uBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,gBADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CA7BA;MAJA;MAkDA;IACA,CA3FA;;IA6FAE;MACA;MACA;MACA,gDAHA,CAIA;;MACA;MACA;MACA;MACA,mBARA,CASA;;MACA;QACA;QACAlB;UACAC,QADA;UACA;UACAC;YACAC,gEADA;YAEAC;UAFA;QAFA,GAFA,CASA;;QACAC;UACAJ,QADA;UACA;UACAC;YACAC,oBADA;YAEAC;UAFA;QAFA;MAOA,CA3BA,CA4BA;;;MACA;;MACA;QACAE;QACAA;MACA;;MACA;QACAC,eADA;QAEAC,uBAFA;QAGAC,2BAHA;QAIAC,SACA;QACA;UACAvB,WADA;UAEAwB,qBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,+BADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CAFA,EAiBA;QACA;UACA7B,WADA;UAEAwB,sBAFA;UAGAC,sBAHA;UAIAK,cAJA;UAKAzB,kBALA;UAMAqB;YAAAC;UAAA,CANA;UAOAC;YAAAD;UAAA,CAPA;UAQAE;QARA,CAlBA,EA4BA;QACA;UACA7B,WADA;UAEAwB,uBAFA;UAGAC,sBAHA;UAIApB;YACAS,UADA;YAEAC;cACAC,gBADA;cAEAC;YAFA;UAFA,EAJA;UAWAS;YAAAC;UAAA,CAXA;UAYAC;YAAAD;UAAA,CAZA;UAaAE;QAbA,CA7BA;MAJA;MAkDA;IACA;;EAlLA;AAjCA", "names": ["name", "props", "id", "type", "required", "maleRatio", "default", "femaleRatio", "data", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "initMaleChart", "tickData", "value", "itemStyle", "color", "borderWidth", "gapData", "combinedData", "animation", "animationDuration", "animationEasing", "series", "radius", "center", "label", "show", "labelLine", "silent", "startAngle", "initFemaleChart"], "sourceRoot": "src/views/smartBrainLargeScreen/components", "sources": ["GenderRatioChart.vue"], "sourcesContent": ["<template>\n  <div class=\"gender-ratio-container\">\n    <div class=\"ratio-item\">\n      <div :id=\"`male-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ maleRatio }}%</span>\n        <span class=\"gender-text\">男</span>\n      </div>\n    </div>\n    <div class=\"ratio-item\">\n      <div :id=\"`female-chart-${id}`\" class=\"chart-container\"></div>\n      <div class=\"ratio-label\">\n        <span class=\"percentage\">{{ femaleRatio }}%</span>\n        <span class=\"gender-text\">女</span>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\n\nexport default {\n  name: 'GenderRatioChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    maleRatio: {\n      type: Number,\n      default: 0\n    },\n    femaleRatio: {\n      type: Number,\n      default: 0\n    }\n  },\n  data () {\n    return {\n      maleChart: null,\n      femaleChart: null\n    }\n  },\n  mounted () {\n    this.initCharts()\n  },\n  beforeDestroy () {\n    if (this.maleChart) {\n      this.maleChart.dispose()\n    }\n    if (this.femaleChart) {\n      this.femaleChart.dispose()\n    }\n  },\n  methods: {\n    initCharts () {\n      this.initMaleChart()\n      this.initFemaleChart()\n    },\n\n    initMaleChart () {\n      const chartContainer = document.getElementById(`male-chart-${this.id}`)\n      if (!chartContainer) return\n      this.maleChart = echarts.init(chartContainer)\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.maleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#00D4FF' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(0, 212, 255, 0.1)',\n                borderWidth: 1\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n      this.maleChart.setOption(option)\n    },\n\n    initFemaleChart () {\n      const chartContainer = document.getElementById(`female-chart-${this.id}`)\n      if (!chartContainer) return\n      this.femaleChart = echarts.init(chartContainer)\n      // 创建刻度线数据 - 总共30个刻度，每个刻度之间有间距\n      const totalTicks = 30\n      const activeTicks = Math.round((this.femaleRatio / 100) * totalTicks)\n      const tickData = []\n      const gapData = []\n      // 创建刻度和间距\n      for (let i = 0; i < totalTicks; i++) {\n        // 刻度线\n        tickData.push({\n          value: 3, // 刻度线的宽度\n          itemStyle: {\n            color: i < activeTicks ? '#FFD700' : 'rgba(255, 255, 255, 0.15)',\n            borderWidth: 0\n          }\n        })\n        // 间距\n        gapData.push({\n          value: 5, // 间距的宽度\n          itemStyle: {\n            color: 'transparent',\n            borderWidth: 0\n          }\n        })\n      }\n      // 合并刻度和间距数据\n      const combinedData = []\n      for (let i = 0; i < totalTicks; i++) {\n        combinedData.push(tickData[i])\n        combinedData.push(gapData[i])\n      }\n      const option = {\n        animation: true,\n        animationDuration: 2000,\n        animationEasing: 'cubicOut',\n        series: [\n          // 内部背景圆\n          {\n            type: 'pie',\n            radius: ['0%', '62%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: 'rgba(255, 215, 0, 0.1)',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 刻度线\n          {\n            type: 'pie',\n            radius: ['75%', '92%'],\n            center: ['50%', '50%'],\n            startAngle: 90,\n            data: combinedData,\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          },\n          // 外层边框圆\n          {\n            type: 'pie',\n            radius: ['98%', '100%'],\n            center: ['50%', '50%'],\n            data: [{\n              value: 100,\n              itemStyle: {\n                color: '#105379',\n                borderWidth: 0\n              }\n            }],\n            label: { show: false },\n            labelLine: { show: false },\n            silent: true\n          }\n        ]\n      }\n      this.femaleChart.setOption(option)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.gender-ratio-container {\n  display: flex;\n  justify-content: space-around;\n  align-items: center;\n  height: 100%;\n\n  .ratio-item {\n    position: relative;\n    width: 182px;\n    height: 172px;\n\n    .chart-container {\n      width: 100%;\n      height: 100%;\n    }\n\n    .ratio-label {\n      position: absolute;\n      top: 50%;\n      left: 50%;\n      transform: translate(-50%, -50%);\n      text-align: center;\n      color: #fff;\n      z-index: 100;\n      pointer-events: none;\n\n      .percentage {\n        display: block;\n        font-size: 28px;\n        font-weight: bold;\n        line-height: 1;\n        margin-bottom: 6px;\n        text-shadow: 0 0 8px rgba(255, 255, 255, 0.3);\n      }\n\n      .gender-text {\n        display: block;\n        font-size: 16px;\n        opacity: 0.9;\n        font-weight: 500;\n      }\n    }\n  }\n}\n</style>\n"]}]}