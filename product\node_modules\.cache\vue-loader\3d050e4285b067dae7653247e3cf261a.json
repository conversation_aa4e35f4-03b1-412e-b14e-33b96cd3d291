{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue?vue&type=style&index=0&id=4beb7f23&lang=scss&scoped=true&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\performanceStatistics\\performanceStatisticsBox.vue", "mtime": 1758769074724}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1655715156000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["performanceStatisticsBox.vue"], "names": [], "mappings": ";AAy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file": "performanceStatisticsBox.vue", "sourceRoot": "src/views/smartBrainLargeScreen/performanceStatistics", "sourcesContent": ["<template>\r\n  <div class=\"big-screen\" ref=\"bigScreen\">\r\n    <div class=\"screen-header\">\r\n      <div class=\"header-left\">\r\n        <span class=\"date-time\">{{ currentTime }}</span>\r\n        <span class=\"weather\">晴 24℃ 东南风</span>\r\n      </div>\r\n      <div class=\"header-center\">\r\n        <img src=\"../../../assets/largeScreen/top_header_txt.png\" alt=\"\" style=\"height: 50px;\">\r\n      </div>\r\n      <div class=\"header-right\">\r\n        <div class=\"header-buttons\">\r\n          <div class=\"header-btn current-module-btn\">\r\n            <span>履职统计</span>\r\n          </div>\r\n          <div class=\"header-btn home-btn\" @click=\"goHome\">\r\n            <span>返回首页</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <div class=\"screen-content\">\r\n      <div class=\"left-panel\">\r\n        <!-- 年度履职汇总 -->\r\n        <div class=\"performance-summary-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">年度履职汇总</span>\r\n          </div>\r\n          <div class=\"performance-summary-content\">\r\n            <div class=\"summary-grid\">\r\n              <div class=\"summary-item\" v-for=\"(item, index) in performanceSummaryData\" :key=\"index\">\r\n                <div class=\"summary-icon\">\r\n                  <img :src=\"item.icon\" :alt=\"item.name\" />\r\n                </div>\r\n                <div class=\"summary-info\">\r\n                  <div class=\"summary-label\">{{ item.name }}</div>\r\n                  <div class=\"summary-value\">{{ item.value }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 会议类型统计 -->\r\n        <div class=\"metting-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">会议类型统计</span>\r\n          </div>\r\n          <div class=\"metting-content\">\r\n            <div class=\"meeting-grid\">\r\n              <div class=\"meeting-item\" v-for=\"(item, index) in meetingTypeData\" :key=\"index\">\r\n                <div class=\"meeting-number\">{{ item.value }}</div>\r\n                <div class=\"meeting-label\">{{ item.name }}</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- 活动类型统计 -->\r\n        <div class=\"activity-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">活动类型统计</span>\r\n          </div>\r\n          <div class=\"activity-content\">\r\n            <BarScrollChart id=\"activityTypeChart\" :showCount=\"12\" :chart-data=\"activityTypeData\"\r\n              :alternate-colors=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <div class=\"right-panel\">\r\n        <!-- 履职数据分析 -->\r\n        <div class=\"performance-analysis-section\">\r\n          <div class=\"header_box\">\r\n            <span class=\"header_text_left\">履职数据分析</span>\r\n          </div>\r\n          <div class=\"performance-content\">\r\n            <div class=\"table-container\">\r\n              <!-- 固定表头 -->\r\n              <div class=\"table-header\">\r\n                <div class=\"header-cell\">姓名</div>\r\n                <div class=\"header-cell\">会议活动</div>\r\n                <div class=\"header-cell\">政协提案</div>\r\n                <div class=\"header-cell\">社情民意</div>\r\n                <div class=\"header-cell\">议政建言</div>\r\n                <div class=\"header-cell\">读书心得</div>\r\n                <div class=\"header-cell\">委员培训</div>\r\n                <div class=\"header-cell\"></div> <!-- 滚动条占位 -->\r\n              </div>\r\n              <!-- 可滚动内容 -->\r\n              <div class=\"table-body\">\r\n                <div class=\"table-row\" v-for=\"(item, index) in performanceData\" :key=\"index\">\r\n                  <div class=\"table-cell name-col\">{{ item.userName }}</div>\r\n                  <div class=\"table-cell meeting-col\">{{ item.meeting }}</div>\r\n                  <div class=\"table-cell proposal-col\">{{ item.proposal }}</div>\r\n                  <div class=\"table-cell opinion-col\">{{ item.social }}</div>\r\n                  <div class=\"table-cell suggestion-col\">{{ item.advise }}\r\n                  </div>\r\n                  <div class=\"table-cell reading-col\">{{ item.activity }}</div>\r\n                  <div class=\"table-cell training-col\">{{ item.others }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { useIndex } from '../screen.js'\r\nimport BarScrollChart from '../components/BarScrollChart.vue'\r\n\r\nexport default {\r\n  name: 'BigScreen',\r\n  components: {\r\n    BarScrollChart\r\n  },\r\n  data () {\r\n    return {\r\n      currentTime: '',\r\n      // 履职数据分析表格数据\r\n      performanceData: [],\r\n      // 活动类型统计数据\r\n      activityTypeData: [],\r\n      // 会议类型统计数据\r\n      meetingTypeData: [\r\n        { name: '全体会议', value: 0 },\r\n        { name: '常委会议', value: 0 },\r\n        { name: '主席会议', value: 0 },\r\n        { name: '其他会议', value: 0 }\r\n      ],\r\n      // 年度履职汇总数据\r\n      performanceSummaryData: [\r\n        { name: '提交提案', value: 1500, icon: require('../../../assets/largeScreen/icon_submit_proposal.png') },\r\n        { name: '提交社情民意', value: 1057, icon: require('../../../assets/largeScreen/icon_submit_social.png') },\r\n        { name: '网络议政', value: 215, icon: require('../../../assets/largeScreen/icon_network.png') },\r\n        { name: '参加会议', value: 361, icon: require('../../../assets/largeScreen/icon_metting_item.png') },\r\n        { name: '参加活动', value: 104, icon: require('../../../assets/largeScreen/icon_activity_item.png') },\r\n        { name: '其他履职', value: 241, icon: require('../../../assets/largeScreen/item_other_duties.png') }\r\n      ]\r\n    }\r\n  },\r\n  computed: {\r\n  },\r\n  mounted () {\r\n    this.initScreen()\r\n    this.updateTime()\r\n    this.timeInterval = setInterval(this.updateTime, 1000)\r\n    this.getData()\r\n  },\r\n  beforeDestroy () {\r\n    if (this.timeInterval) {\r\n      clearInterval(this.timeInterval)\r\n    }\r\n  },\r\n  methods: {\r\n    initScreen () {\r\n      const { calcRate, windowDraw } = useIndex(this.$refs.bigScreen)\r\n      calcRate()\r\n      windowDraw()\r\n    },\r\n    updateTime () {\r\n      const now = new Date()\r\n      this.currentTime = now.toLocaleString('zh-CN', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit'\r\n      })\r\n    },\r\n    getData () {\r\n      this.getDutyStatstics()\r\n      this.getDutyAnalysis()\r\n      this.getDutyMeetingType()\r\n      this.getDutyYearOverall()\r\n    },\r\n    // 获取履职统计\r\n    async getDutyStatstics () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyStatistics()\r\n      this.performanceData = res.data\r\n    },\r\n    // 获取活动类型统计\r\n    async getDutyAnalysis () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'activity' })\r\n      this.activityTypeData = res.data\r\n    },\r\n    // 获取会议类型统计\r\n    async getDutyMeetingType () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'meeting' })\r\n      this.meetingTypeData[0].value = res.data[6].count\r\n      this.meetingTypeData[1].value = res.data[11].count\r\n      this.meetingTypeData[2].value = res.data[1].count\r\n      this.meetingTypeData[3].value = res.data[7].count\r\n    },\r\n    // 获取年度履职汇总\r\n    async getDutyYearOverall () {\r\n      const res = await this.$api.smartBrainLargeScreen.dutyAnalysis({ type: 'overall' })\r\n      this.performanceSummaryData[0].value = res.data[6].count\r\n      this.performanceSummaryData[1].value = res.data[11].count\r\n      this.performanceSummaryData[2].value = res.data[1].count\r\n      this.performanceSummaryData[3].value = res.data[7].count\r\n      this.performanceSummaryData[4].value = res.data[7].count\r\n      this.performanceSummaryData[5].value = res.data[7].count\r\n    },\r\n    // 返回首页\r\n    goHome () {\r\n      this.$router.push({ path: '/homeBox' })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.big-screen {\r\n  width: 1920px;\r\n  height: 1080px;\r\n  position: relative;\r\n  top: 50%;\r\n  left: 50%;\r\n  transform: translate(-50%, -50%);\r\n  transform-origin: left top;\r\n  background: url('../../../assets/largeScreen/bg.jpg') no-repeat;\r\n  background-size: cover;\r\n  background-position: center;\r\n\r\n  .screen-header {\r\n    background: url('../../../assets/largeScreen/top_header_bg.png') no-repeat;\r\n    background-size: 100% 100%;\r\n    background-position: center;\r\n    height: 65px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 40px;\r\n\r\n    .header-left {\r\n      display: flex;\r\n      gap: 20px;\r\n      font-size: 14px;\r\n      color: #8cc8ff;\r\n      flex: 1;\r\n    }\r\n\r\n    .header-center {\r\n      width: 60%;\r\n      text-align: center;\r\n    }\r\n\r\n    .header-right {\r\n      flex: 1;\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n\r\n      .header-buttons {\r\n        display: flex;\r\n        gap: 15px;\r\n\r\n        .header-btn {\r\n          height: 42px;\r\n          line-height: 42px;\r\n          padding: 0 16px;\r\n          cursor: pointer;\r\n          transition: all 0.3s ease;\r\n          position: relative;\r\n          overflow: hidden;\r\n\r\n          &::before {\r\n            content: '';\r\n            position: absolute;\r\n            top: 0;\r\n            left: -100%;\r\n            width: 100%;\r\n            height: 100%;\r\n            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\r\n            transition: left 0.5s;\r\n          }\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 1) 0%, rgba(0, 120, 220, 1) 100%);\r\n            border-color: rgba(0, 181, 254, 1);\r\n            transform: translateY(-2px);\r\n            box-shadow: 0 4px 12px rgba(0, 181, 254, 0.4);\r\n\r\n            &::before {\r\n              left: 100%;\r\n            }\r\n          }\r\n\r\n          &.current-module-btn {\r\n            background: url('../../../assets/largeScreen/icon_committee.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: bold;\r\n            font-size: 16px;\r\n            color: #FFFFFF;\r\n          }\r\n\r\n          &.home-btn {\r\n            background: url('../../../assets/largeScreen/icon_back_home.png') no-repeat;\r\n            background-size: 100% 100%;\r\n            background-position: center;\r\n            font-weight: 400;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n          }\r\n\r\n          &.area-select-btn {\r\n            background: linear-gradient(135deg, rgba(0, 181, 254, 0.2) 0%, rgba(0, 120, 220, 0.2) 100%);\r\n            border: 1px solid rgba(0, 181, 254, 0.5);\r\n            border-radius: 6px;\r\n            font-weight: 500;\r\n            font-size: 16px;\r\n            color: #1FC6FF;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n            min-width: 120px;\r\n\r\n            .dropdown-icon {\r\n              margin-left: 8px;\r\n              font-size: 12px;\r\n              transition: transform 0.3s ease;\r\n              color: #1FC6FF;\r\n\r\n              &.active {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, rgba(0, 181, 254, 0.3) 0%, rgba(0, 120, 220, 0.3) 100%);\r\n              border-color: rgba(0, 181, 254, 0.8);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .screen-content {\r\n    height: calc(100% - 65px);\r\n    display: flex;\r\n    padding: 20px;\r\n    gap: 20px;\r\n\r\n    .header_box {\r\n      position: absolute;\r\n      top: 15px;\r\n      left: 24px;\r\n      right: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      .header_text_left {\r\n        font-weight: bold;\r\n        font-size: 20px;\r\n        color: #FFFFFF;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .header_text_right {\r\n        font-size: 15px;\r\n        color: #FFD600;\r\n      }\r\n\r\n      .header_text_center {\r\n        font-size: 15px;\r\n        color: #FFFFFF;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        span {\r\n          font-weight: 500;\r\n          font-size: 24px;\r\n          color: #02FBFB;\r\n          margin: 0 10px 0 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .left-panel {\r\n      flex: 1;\r\n      display: grid;\r\n      grid-template-columns: 1fr 1fr;\r\n      grid-template-rows: 1fr 1fr;\r\n      gap: 20px;\r\n      height: 100%;\r\n    }\r\n\r\n    .right-panel {\r\n      width: 922px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 20px;\r\n    }\r\n\r\n    // 左侧面板样式\r\n    .left-panel {\r\n\r\n      // 年度履职汇总\r\n      .performance-summary-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_performance_summary_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1; // 第一列\r\n        grid-row: 1; // 第一行\r\n\r\n        .performance-summary-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n          display: flex;\r\n          justify-content: center;\r\n          padding-left: 20px;\r\n\r\n          .summary-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr 1fr;\r\n            gap: 12px;\r\n            width: 100%;\r\n\r\n            .summary-item {\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              .summary-icon {\r\n                width: 64px;\r\n                height: 64px;\r\n                margin-right: 12px;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n                border-radius: 6px;\r\n\r\n                img {\r\n                  width: 100%;\r\n                  height: 100%;\r\n                  object-fit: contain;\r\n                }\r\n              }\r\n\r\n              .summary-info {\r\n                flex: 1;\r\n\r\n                .summary-label {\r\n                  font-weight: 400;\r\n                  font-size: 15px;\r\n                  color: #B4C0CC;\r\n                  margin-bottom: 8px;\r\n                  line-height: 20px;\r\n                }\r\n\r\n                .summary-value {\r\n                  font-family: DIN, DIN;\r\n                  font-weight: 500;\r\n                  font-size: 32px;\r\n                  color: #FFFFFF;\r\n                  line-height: 30px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 会议类型统计\r\n      .metting-section {\r\n        height: 390px;\r\n        background: url('../../../assets/largeScreen/icon_metting_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 2; // 第二列\r\n        grid-row: 1; // 第一行\r\n\r\n        .metting-content {\r\n          margin-top: 60px;\r\n          height: calc(100% - 70px);\r\n\r\n          .meeting-grid {\r\n            display: grid;\r\n            grid-template-columns: 1fr 1fr;\r\n            grid-template-rows: 1fr 1fr;\r\n            gap: 15px;\r\n            height: 100%;\r\n\r\n            .meeting-item {\r\n              width: 200px;\r\n              height: 125px;\r\n              background: url('../../../assets/largeScreen/icon_metting_type.png') no-repeat;\r\n              background-size: 100% 100%;\r\n              background-position: center;\r\n              display: flex;\r\n              flex-direction: column;\r\n              justify-content: center;\r\n              align-items: center;\r\n              padding: 15px;\r\n              cursor: pointer;\r\n\r\n              .meeting-number {\r\n                font-weight: 500;\r\n                font-size: 32px;\r\n                color: #FFFFFF;\r\n                margin-bottom: 10px;\r\n                font-family: 'DIN', Arial, sans-serif;\r\n                transition: all 0.3s ease;\r\n              }\r\n\r\n              .meeting-label {\r\n                font-size: 16px;\r\n                color: #FFFFFF;\r\n                font-weight: 500;\r\n                text-align: center;\r\n                line-height: 1.2;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      // 活动类型统计\r\n      .activity-section {\r\n        height: 550px;\r\n        background: url('../../../assets/largeScreen/icon_activity_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        grid-column: 1 / -1; // 跨越两列（活动类型统计在第二行）\r\n        grid-row: 2; // 第二行\r\n\r\n        .activity-content {\r\n          margin-top: 30px;\r\n          height: calc(100% - 30px);\r\n        }\r\n      }\r\n    }\r\n\r\n    .right-panel {\r\n\r\n      // 履职数据分析\r\n      .performance-analysis-section {\r\n        background: url('../../../assets/largeScreen/icon_performance_bg.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        background-position: center;\r\n        position: relative;\r\n        padding: 20px;\r\n        height: 100%;\r\n\r\n        .performance-content {\r\n          margin-top: 50px;\r\n          height: calc(100% - 50px);\r\n\r\n          .table-container {\r\n            height: 100%;\r\n            display: flex;\r\n            flex-direction: column;\r\n            border: 1px solid #117090;\r\n            overflow: hidden;\r\n            --name-col-width: 124px;\r\n            --scrollbar-width: 6px;\r\n\r\n            .table-header {\r\n              display: grid;\r\n              grid-template-columns: var(--name-col-width) repeat(6, 1fr) var(--scrollbar-width);\r\n              border-bottom: 1px solid #117090;\r\n              position: sticky;\r\n              top: 0;\r\n              z-index: 10;\r\n\r\n              .header-cell {\r\n                height: 40px;\r\n                line-height: 40px;\r\n                text-align: center;\r\n                font-weight: 400;\r\n                color: #B4C0CC;\r\n                font-size: 15px;\r\n                border-right: 1px solid #117090;\r\n                display: flex;\r\n                align-items: center;\r\n                justify-content: center;\r\n\r\n                &:last-child {\r\n                  border-right: none;\r\n                  background: transparent;\r\n                  border: none;\r\n                }\r\n\r\n                // &.name-col {\r\n                //   background: rgba(0, 100, 180, 0.9);\r\n                //   font-weight: 600;\r\n                // }\r\n              }\r\n            }\r\n\r\n            .table-body {\r\n              flex: 1;\r\n              overflow-y: auto;\r\n\r\n              &::-webkit-scrollbar {\r\n                width: 6px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-track {\r\n                background: rgba(0, 30, 60, 0.3);\r\n                border-radius: 3px;\r\n              }\r\n\r\n              &::-webkit-scrollbar-thumb {\r\n                background: rgba(0, 212, 255, 0.4);\r\n                border-radius: 3px;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.6);\r\n                }\r\n              }\r\n\r\n              .table-row {\r\n                display: grid;\r\n                grid-template-columns: var(--name-col-width) repeat(6, 1fr);\r\n                border-bottom: 1px solid #117090;\r\n                transition: all 0.3s ease;\r\n\r\n                &:hover {\r\n                  background: rgba(0, 212, 255, 0.1);\r\n                }\r\n\r\n                .table-cell {\r\n                  padding: 12px 8px;\r\n                  text-align: center;\r\n                  color: #FFFFFF;\r\n                  font-size: 14px;\r\n                  border-right: 1px solid #117090;\r\n                  transition: all 0.3s ease;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  background: rgba(31, 198, 255, 0.16);\r\n\r\n                  &:last-child {\r\n                    border-right: none;\r\n                  }\r\n\r\n                  &.name-col {\r\n                    background: rgba(31, 198, 255, 0.16);\r\n                    color: #FFF;\r\n                    font-weight: 500;\r\n                  }\r\n\r\n                  &.meeting-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    color: #59F7CA;\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                  }\r\n\r\n                  &.proposal-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #00FFF7;\r\n                  }\r\n\r\n                  &.opinion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF386B;\r\n                  }\r\n\r\n                  &.suggestion-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #81C4E4;\r\n                  }\r\n\r\n                  &.reading-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #387BFD;\r\n                  }\r\n\r\n                  &.training-col {\r\n                    // background: rgba(10, 63, 111, 0.4);\r\n                    font-weight: 500;\r\n                    font-size: 16px;\r\n                    color: #FF911F;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}