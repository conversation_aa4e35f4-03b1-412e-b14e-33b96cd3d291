{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\HorizontalBarChart.vue", "mtime": 1758776011577}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HorizontalBarChart.vue"], "names": [], "mappings": ";AAkBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HorizontalBarChart.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"horizontal-bar-chart\">\n    <div class=\"chart-container\">\n      <div v-for=\"(item, index) in chartData\" :key=\"index\" class=\"chart-row\">\n        <div class=\"row-label\">{{ item.name }}</div>\n        <div class=\"row-bar-container\">\n          <div class=\"row-bar\">\n            <div v-for=\"(segment, segIndex) in getSegments(item.value)\" :key=\"segIndex\" class=\"bar-segment\"\n              :class=\"{ 'filled': segIndex < getFilledSegments(item.value) }\"></div>\n          </div>\n          <div class=\"row-value\">{{ item.value }}人</div>\n        </div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HorizontalBarChart',\n  props: {\n    chartData: {\n      type: Array,\n      default: () => []\n    },\n    // 每个分段代表的数值\n    segmentValue: {\n      type: Number,\n      default: 10\n    },\n    // 最大分段数（固定总分段数，不随数据变化）\n    maxSegments: {\n      type: Number,\n      default: 25\n    },\n    // 进度条的最大值（用于计算比例，如果不设置则自动计算）\n    maxValue: {\n      type: Number,\n      default: null\n    },\n    // 最大值的缩放比例（数据最大值的倍数）\n    maxValueScale: {\n      type: Number,\n      default: 1.1\n    }\n  },\n\n  computed: {\n    // 计算总分段数（固定值，不随数据变化）\n    totalSegments () {\n      return this.maxSegments\n    },\n    // 动态计算的最大值\n    dynamicMaxValue () {\n      // 如果手动设置了maxValue，则使用设置的值\n      if (this.maxValue !== null) {\n        return this.maxValue\n      }\n      // 否则根据数据自动计算\n      if (!this.chartData.length) return 100\n      const dataMaxValue = Math.max(...this.chartData.map(item => item.value))\n      // 返回数据最大值的倍数，确保最大值不会撑满\n      const calculatedMax = Math.ceil(dataMaxValue * this.maxValueScale)\n      console.log(`数据最大值: ${dataMaxValue}, 缩放比例: ${this.maxValueScale}, 计算的最大值: ${calculatedMax}`)\n      return calculatedMax\n    }\n  },\n  methods: {\n    // 生成分段数组\n    getSegments (value) {\n      const segments = []\n      for (let i = 0; i < this.totalSegments; i++) {\n        segments.push(i)\n      }\n      return segments\n    },\n    // 计算应该填充的分段数\n    getFilledSegments (value) {\n      if (value <= 0) return 0\n      // 计算按比例应该填充的分段数\n      const proportionalSegments = Math.floor((value / this.dynamicMaxValue) * this.totalSegments)\n      // 确保有数值的项目至少显示1个分段\n      return Math.max(1, proportionalSegments)\n    }\n  }\n}\n</script>\n\n<style scoped>\n.horizontal-bar-chart {\n  width: 100%;\n  height: 100%;\n  padding-top: 10px;\n}\n\n.chart-container {\n  display: flex;\n  flex-direction: column;\n  gap: 22px;\n  min-height: fit-content;\n}\n\n.chart-row {\n  display: flex;\n  align-items: center;\n  position: relative;\n}\n\n.row-label {\n  width: 50px;\n  color: #FFFFFF;\n  font-size: 14px;\n  font-weight: 500;\n  text-align: left;\n  flex-shrink: 0;\n  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);\n}\n\n.row-bar-container {\n  flex: 1;\n  display: flex;\n  align-items: center;\n  margin-left: 15px;\n}\n\n.row-bar {\n  flex: 1;\n  height: 15px;\n  display: flex;\n  align-items: center;\n  background: #09306B;\n  border: 1px solid #979797;\n  position: relative;\n  overflow: hidden;\n  padding: 1px;\n  /* 右边斜切效果 */\n  /* clip-path: polygon(0 0, calc(100% - 8px) 0, 100% 100%, 0 100%); */\n}\n\n.bar-segment {\n  flex: 1;\n  height: 10px;\n  background: rgba(0, 100, 200, 0.2);\n  transition: all 0.4s ease;\n  position: relative;\n  border: none;\n  /* 倾斜的格子效果 */\n  clip-path: polygon(15% 0%, 100% 0%, 85% 100%, 0% 100%);\n  margin-right: 1px;\n}\n\n.bar-segment:last-child {\n  margin-right: 0;\n}\n\n.bar-segment.filled {\n  background: linear-gradient(135deg,\n      #00D4FF 0%,\n      #0099CC 30%,\n      #00AADD 70%,\n      #33E0FF 100%);\n}\n\n.row-value {\n  color: #FFFFFF;\n  font-size: 14px;\n  width: 70px;\n  text-align: right;\n}\n\n/* 悬停效果 */\n.chart-row:hover .row-bar {\n  border-color: rgba(0, 212, 255, 0.8);\n  box-shadow:\n    inset 0 1px 2px rgba(0, 0, 0, 0.3),\n    0 0 15px rgba(0, 212, 255, 0.4);\n}\n\n.chart-row:hover .bar-segment.filled {\n  background: linear-gradient(135deg,\n      #33E0FF 0%,\n      #00AADD 30%,\n      #00BBEE 70%,\n      #55F0FF 100%);\n  box-shadow:\n    0 0 12px rgba(0, 212, 255, 0.7),\n    inset 0 1px 2px rgba(255, 255, 255, 0.4);\n  transform: scale(1.05);\n}\n\n.chart-row:hover .row-value {\n  color: #00D4FF;\n  text-shadow: 0 0 8px rgba(0, 212, 255, 0.6);\n}\n\n.chart-row:hover .row-label {\n  color: #00D4FF;\n  text-shadow: 0 0 6px rgba(0, 212, 255, 0.4);\n}\n</style>\n"]}]}