<template>
  <div class="party-distribution-chart">
    <!-- 3D饼图容器 -->
    <div class="container">
      <div class="chartsGl" :id="id"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'

export default {
  name: 'PartyDistributionChart',
  props: {
    id: {
      type: String,
      required: true
    },
    chartData: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  data () {
    return {
      chart: null,
      option: {},
      isAnimating: false,
      animationTimer: null
    }
  },
  mounted () {
    this.initChart()
  },
  beforeDestroy () {
    if (this.animationTimer) {
      clearTimeout(this.animationTimer)
    }
    if (this.chart) {
      this.chart.dispose()
    }
    window.removeEventListener('resize', this.resizeChart)
  },
  watch: {
    chartData: {
      handler () {
        this.renderChart()
      },
      deep: true
    }
  },
  methods: {
    // 初始化构建3D饼图
    initChart () {
      const chartContainer = document.getElementById(this.id)
      if (!chartContainer) return

      this.chart = echarts.init(chartContainer)

      // 转换数据格式
      const optionData = this.convertChartData()

      // 传入数据生成 option ; getPie3D(数据，透明的空心占比（调节中间空心范围的0就是普通饼1就很镂空）)
      this.option = this.getPie3D(optionData, 0.85)

      // 将配置项设置进去
      this.chart.setOption(this.option)

      // 鼠标移动上去特效效果
      this.bindListen(this.chart)

      window.addEventListener('resize', this.resizeChart)
    },

    // 转换数据格式
    convertChartData () {
      if (!this.chartData || this.chartData.length === 0) {
        return [
          {
            name: '中国共产党',
            value: 32,
            itemStyle: {
              color: 'rgba(255, 107, 107, 0.8)'
            }
          },
          {
            name: '民革',
            value: 15,
            itemStyle: {
              color: 'rgba(78, 205, 196, 0.8)'
            }
          },
          {
            name: '民盟',
            value: 14,
            itemStyle: {
              color: 'rgba(69, 183, 209, 0.8)'
            }
          }
        ]
      }

      // 预定义颜色数组
      const colors = [
        'rgba(255, 107, 107, 0.8)', 'rgba(78, 205, 196, 0.8)', 'rgba(69, 183, 209, 0.8)',
        'rgba(150, 206, 180, 0.8)', 'rgba(255, 234, 167, 0.8)', 'rgba(221, 160, 221, 0.8)',
        'rgba(152, 216, 200, 0.8)', 'rgba(247, 220, 111, 0.8)', 'rgba(187, 143, 206, 0.8)',
        'rgba(133, 193, 233, 0.8)'
      ]

      return this.chartData.map((item, index) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: colors[index % colors.length]
        }
      }))
    },
    // 配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比
    getPie3D (pieData, internalDiameterRatio) {
      const that = this
      const series = []
      let sumValue = 0
      let startValue = 0
      let endValue = 0
      let legendData = []
      let legendBfb = []
      const k = 1 - internalDiameterRatio
      pieData.sort((a, b) => {
        return (b.value - a.value)
      })

      // 标记前三名，用于显示线条指示
      pieData.forEach((item, index) => {
        item.isTopThree = index < 3
        item.rank = index + 1
      })

      // 调试输出数据顺序
      console.log('3D饼图数据顺序:', pieData.map(item => `${item.name}: ${item.value}`))
      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value
        const seriesItem = {
          // 系统名称
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          // 是否为参数曲面（是）
          parametric: true,
          // 曲面图网格线（否）上面一根一根的
          wireframe: {
            show: false
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k
          },
          // 设置饼图在容器中的位置(目前没发现啥用)
          center: ['80%', '100%'],
          radius: '60%'
        }

        // 曲面的颜色、不透明度等样式。
        if (typeof pieData[i].itemStyle !== 'undefined') {
          const itemStyle = {}
          if (typeof pieData[i].itemStyle.color !== 'undefined') {
            itemStyle.color = pieData[i].itemStyle.color
          }
          if (typeof pieData[i].itemStyle.opacity !== 'undefined') {
            itemStyle.opacity = pieData[i].itemStyle.opacity
          }
          seriesItem.itemStyle = itemStyle
        }
        series.push(seriesItem)
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      legendData = []
      legendBfb = []
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value
        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = that.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,
          false, false, k, series[i].pieData.value)
        startValue = endValue
        const bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4)
        legendData.push({
          name: series[i].name,
          value: bfb
        })
        legendBfb.push({
          name: series[i].name,
          value: bfb
        })
      }

      // (第二个参数可以设置你这个环形的高低程度)
      const boxHeight = this.getHeight3D(series, 20) // 通过传参设定3d饼/环的高度
      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      const option = {
        // 图例组件
        legend: {
          data: legendData,
          // 图例列表的布局朝向 - 垂直布局
          orient: 'vertical',
          right: '5%', // 距离右边5%
          top: 'center', // 垂直居中
          itemWidth: 14,
          itemHeight: 14,
          itemGap: 30, // 增加间距
          textStyle: {
            color: '#A1E2FF',
            fontSize: 13
          },
          // 简化格式化，只显示名称和百分比
          formatter: function (param) {
            const item = legendBfb.filter(item => item.name === param)[0]
            const bfs = that.fomatFloat(item.value * 100, 1) + '%'
            return `${item.name}: ${bfs}`
          }
        },
        // 移动上去提示的文本内容
        tooltip: {
          formatter: params => {
            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
              const bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) *
                100).toFixed(2)
              return `${params.seriesName}<br/>` +
                `<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};"></span>` +
                `${bfb}%`
            }
          }
        },
        // 这个可以变形
        xAxis3D: {
          min: -1,
          max: 1
        },
        yAxis3D: {
          min: -1,
          max: 1
        },
        zAxis3D: {
          min: -1,
          max: 1
        },
        // 此处是修改样式的重点
        grid3D: {
          show: false,
          boxHeight: boxHeight, // 圆环的高度
          // 这是饼图的位置 - 调整到合适位置
          top: '-5%', // 适中的垂直位置
          left: '-8%', // 适中的水平位置
          width: '60%', // 限制图表宽度，为右侧图例留出空间
          // 优化性能配置
          postEffect: {
            enable: false // 关闭后处理效果，提升性能
          },
          light: {
            main: {
              intensity: 1.2,
              shadow: false // 关闭阴影，提升性能
            },
            ambient: {
              intensity: 0.3
            }
          },
          viewControl: { // 3d效果可以放大、旋转等，请自己去查看官方配置
            alpha: 25, // 适中的俯视角度
            beta: 0, // 水平旋转角度
            distance: 150, // 适中的观察距离
            center: [0, 0, 0], // 3D场景中心点
            rotateSensitivity: 0, // 设置为0无法旋转
            zoomSensitivity: 0, // 设置为0无法缩放
            panSensitivity: 0, // 设置为0无法平移
            autoRotate: false, // 自动旋转
            animation: false // 关闭动画，提升性能
          }
        },
        series: [
          ...series
          // 添加前三名的线条指示
          // {
          //   type: 'pie',
          //   radius: ['30%', '60%'], // 调整半径以匹配3D饼图的视觉范围
          //   center: ['30%', '45%'], // 微调中心点以更精确匹配3D饼图
          //   startAngle: 30, // 进一步调整起始角度
          //   clockwise: false, // 顺时针方向
          //   // 使用完整数据以确保角度对应正确，但只为前三名设置标签
          //   data: pieData.map(item => ({
          //     name: item.name,
          //     value: item.value,
          //     itemStyle: {
          //       color: 'transparent' // 透明，只显示标签线
          //     },
          //     label: {
          //       show: item.isTopThree, // 只有前三名显示标签
          //       position: 'outside',
          //       fontSize: 12,
          //       color: '#FFFFFF',
          //       fontWeight: 'bold',
          //       backgroundColor: 'rgba(0, 0, 0, 0.7)',
          //       borderColor: '#A1E2FF',
          //       borderWidth: 1,
          //       borderRadius: 4,
          //       padding: [3, 6],
          //       formatter: `${item.name} ${item.value}人`
          //     },
          //     labelLine: {
          //       show: item.isTopThree, // 只有前三名显示线条
          //       length: 25, // 适中的第一段线条长度
          //       length2: 15, // 适中的第二段线条长度
          //       lineStyle: {
          //         color: '#A1E2FF',
          //         width: 2,
          //         type: 'solid'
          //       }
          //     }
          //   })),
          //   // 全局标签配置（会被数据项中的配置覆盖）
          //   label: {
          //     show: false, // 默认不显示
          //     position: 'outside',
          //     fontSize: 12,
          //     color: '#FFFFFF',
          //     fontWeight: 'bold',
          //     backgroundColor: 'rgba(0, 0, 0, 0.7)',
          //     borderColor: '#A1E2FF',
          //     borderWidth: 1,
          //     borderRadius: 4,
          //     padding: [3, 6],
          //     formatter: function (params) {
          //       return `${params.name} ${params.value}人`
          //     }
          //   },
          //   labelLine: {
          //     show: false, // 默认不显示
          //     length: 25,
          //     length2: 15,
          //     lineStyle: {
          //       color: '#A1E2FF',
          //       width: 2,
          //       type: 'solid'
          //     }
          //   },
          //   silent: true,
          //   z: 10
          // }
        ]
      }
      return option
    },
    // 获取3d饼图的最高扇区的高度
    getHeight3D (series, height) {
      series.sort((a, b) => {
        return (b.pieData.value - a.pieData.value)
      })
      return height * 25 / series[0].pieData.value
    },

    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
    getParametricEquation (startRatio, endRatio, isSelected, isHovered, k, h) {
      // 计算
      const midRatio = (startRatio + endRatio) / 2
      const startRadian = startRatio * Math.PI * 2
      const endRadian = endRatio * Math.PI * 2
      const midRadian = midRatio * Math.PI * 2
      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        isSelected = false
      }
      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3
      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0
      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      const hoverRate = isHovered ? 1.05 : 1
      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 16
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 10
        },
        x: function (u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        y: function (u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        z: function (u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u)
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1
          }
          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1
        }
      }
    },

    // 防抖函数
    debounce (func, wait) {
      let timeout
      return function executedFunction (...args) {
        const later = () => {
          clearTimeout(timeout)
          func(...args)
        }
        clearTimeout(timeout)
        timeout = setTimeout(later, wait)
      }
    },

    // 优化的更新图表方法
    updateChartOption (option) {
      if (this.isAnimating) return
      this.isAnimating = true

      // 使用requestAnimationFrame优化渲染
      requestAnimationFrame(() => {
        if (this.chart) {
          this.chart.setOption(option, false, false) // 关闭动画和合并
        }
        this.isAnimating = false
      })
    },

    // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
    bindListen (myChart) {
      const that = this
      let selectedIndex = ''
      let hoveredIndex = ''
      // 监听点击事件，实现选中效果（单选）
      myChart.on('click', function (params) {
        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
        const isSelected = !that.option.series[params.seriesIndex].pieStatus.selected
        const isHovered = that.option.series[params.seriesIndex].pieStatus.hovered
        const k = that.option.series[params.seriesIndex].pieStatus.k
        const startRatio = that.option.series[params.seriesIndex].pieData.startRatio
        const endRatio = that.option.series[params.seriesIndex].pieData.endRatio
        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
        if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {
          that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(
            that.option.series[selectedIndex].pieData.startRatio,
            that.option.series[selectedIndex].pieData.endRatio,
            false,
            false,
            k,
            that.option.series[selectedIndex].pieData.value
          )
          that.option.series[selectedIndex].pieStatus.selected = false
        }
        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
        that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,
          isSelected,
          isHovered, k, that.option.series[params.seriesIndex].pieData.value)
        that.option.series[params.seriesIndex].pieStatus.selected = isSelected
        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
        if (isSelected) {
          selectedIndex = params.seriesIndex
        }
        // 使用优化的更新方法
        that.updateChartOption(that.option)
      })

      // 防抖的mouseover处理
      const debouncedMouseover = this.debounce((params) => {
        that.handleMouseover(params, hoveredIndex, (newIndex) => {
          hoveredIndex = newIndex
        })
      }, 16) // 约60fps

      // 监听 mouseover，近似实现高亮（放大）效果
      myChart.on('mouseover', debouncedMouseover)

      // 修正取消高亮失败的 bug
      myChart.on('globalout', function () {
        if (hoveredIndex !== '') {
          const isSelected = that.option.series[hoveredIndex].pieStatus.selected
          const isHovered = false
          const k = that.option.series[hoveredIndex].pieStatus.k
          const startRatio = that.option.series[hoveredIndex].pieData.startRatio
          const endRatio = that.option.series[hoveredIndex].pieData.endRatio

          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(
            startRatio, endRatio, isSelected, isHovered, k,
            that.option.series[hoveredIndex].pieData.value
          )
          that.option.series[hoveredIndex].pieStatus.hovered = isHovered
          hoveredIndex = ''

          // 使用优化的更新方法
          that.updateChartOption(that.option)
        }
      })
    },

    // 提取mouseover处理逻辑
    handleMouseover (params, hoveredIndex, setHoveredIndex) {
      // 如果触发 mouseover 的扇形当前已高亮，则不做操作
      if (hoveredIndex === params.seriesIndex) {
        return // 已经高亮，不做操作
      }

      // 准备重新渲染扇形所需的参数
      let isSelected, isHovered, startRatio, endRatio, k

      // 如果当前有高亮的扇形，取消其高亮状态
      if (hoveredIndex !== '') {
        isSelected = this.option.series[hoveredIndex].pieStatus.selected
        isHovered = false
        startRatio = this.option.series[hoveredIndex].pieData.startRatio
        endRatio = this.option.series[hoveredIndex].pieData.endRatio
        k = this.option.series[hoveredIndex].pieStatus.k

        this.option.series[hoveredIndex].parametricEquation = this.getParametricEquation(
          startRatio, endRatio, isSelected, isHovered, k,
          this.option.series[hoveredIndex].pieData.value
        )
        this.option.series[hoveredIndex].pieStatus.hovered = isHovered
      }

      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮
      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {
        isSelected = this.option.series[params.seriesIndex].pieStatus.selected
        isHovered = true
        startRatio = this.option.series[params.seriesIndex].pieData.startRatio
        endRatio = this.option.series[params.seriesIndex].pieData.endRatio
        k = this.option.series[params.seriesIndex].pieStatus.k

        this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(
          startRatio, endRatio, isSelected, isHovered, k,
          this.option.series[params.seriesIndex].pieData.value + 5
        )
        this.option.series[params.seriesIndex].pieStatus.hovered = isHovered
        setHoveredIndex(params.seriesIndex)
      }

      // 使用优化的更新方法
      this.updateChartOption(this.option)
    },

    // 这是一个自定义计算的方法
    fomatFloat (num, n) {
      var f = parseFloat(num)
      if (isNaN(f)) {
        return false
      }
      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂
      var s = f.toString()
      var rs = s.indexOf('.')
      // 判定如果是整数，增加小数点再补0
      if (rs < 0) {
        rs = s.length
        s += '.'
      }
      while (s.length <= rs + n) {
        s += '0'
      }
      return s
    },

    resizeChart () {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.party-distribution-chart {
  width: 100%;
  height: 100%;
}

// 饼图(外面的容器)
.container {
  width: 100%;
  height: 100%;
}

// 饼图的大小
.chartsGl {
  height: 100%;
  width: 100%;
}
</style>
