{"remainingRequest": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue?vue&type=script&lang=js&", "dependencies": [{"path": "D:\\zy\\xm\\pc\\qdzx\\product\\src\\views\\smartBrainLargeScreen\\components\\PieChart3D.vue", "mtime": 1758774945802}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\babel-loader\\lib\\index.js", "mtime": 315532800000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\zy\\xm\\pc\\qdzx\\product\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1655715099000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["PieChart3D.vue"], "names": [], "mappings": ";AAUA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "PieChart3D.vue", "sourceRoot": "src/views/smartBrainLargeScreen/components", "sourcesContent": ["<template>\n  <div class=\"party-distribution-chart\">\n    <!-- 3D饼图容器 -->\n    <div class=\"container\">\n      <div class=\"chartsGl\" :id=\"id\"></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as echarts from 'echarts'\nimport 'echarts-gl'\n\nexport default {\n  name: 'PartyDistributionChart',\n  props: {\n    id: {\n      type: String,\n      required: true\n    },\n    chartData: {\n      type: Array,\n      required: true,\n      default: () => []\n    }\n  },\n  data () {\n    return {\n      chart: null,\n      option: {},\n      isAnimating: false,\n      animationTimer: null\n    }\n  },\n  mounted () {\n    this.initChart()\n  },\n  beforeDestroy () {\n    if (this.animationTimer) {\n      clearTimeout(this.animationTimer)\n    }\n    if (this.chart) {\n      this.chart.dispose()\n    }\n    window.removeEventListener('resize', this.resizeChart)\n  },\n  watch: {\n    chartData: {\n      handler () {\n        this.renderChart()\n      },\n      deep: true\n    }\n  },\n  methods: {\n    // 初始化构建3D饼图\n    initChart () {\n      const chartContainer = document.getElementById(this.id)\n      if (!chartContainer) return\n\n      this.chart = echarts.init(chartContainer)\n\n      // 转换数据格式\n      const optionData = this.convertChartData()\n\n      // 传入数据生成 option ; getPie3D(数据，透明的空心占比（调节中间空心范围的0就是普通饼1就很镂空）)\n      this.option = this.getPie3D(optionData, 0.85)\n\n      // 将配置项设置进去\n      this.chart.setOption(this.option)\n\n      // 鼠标移动上去特效效果\n      this.bindListen(this.chart)\n\n      window.addEventListener('resize', this.resizeChart)\n    },\n\n    // 转换数据格式\n    convertChartData () {\n      if (!this.chartData || this.chartData.length === 0) {\n        return [\n          {\n            name: '中国共产党',\n            value: 32,\n            itemStyle: {\n              color: 'rgba(255, 107, 107, 0.8)'\n            }\n          },\n          {\n            name: '民革',\n            value: 15,\n            itemStyle: {\n              color: 'rgba(78, 205, 196, 0.8)'\n            }\n          },\n          {\n            name: '民盟',\n            value: 14,\n            itemStyle: {\n              color: 'rgba(69, 183, 209, 0.8)'\n            }\n          }\n        ]\n      }\n\n      // 预定义颜色数组\n      const colors = [\n        'rgba(255, 107, 107, 0.8)', 'rgba(78, 205, 196, 0.8)', 'rgba(69, 183, 209, 0.8)',\n        'rgba(150, 206, 180, 0.8)', 'rgba(255, 234, 167, 0.8)', 'rgba(221, 160, 221, 0.8)',\n        'rgba(152, 216, 200, 0.8)', 'rgba(247, 220, 111, 0.8)', 'rgba(187, 143, 206, 0.8)',\n        'rgba(133, 193, 233, 0.8)'\n      ]\n\n      return this.chartData.map((item, index) => ({\n        name: item.name,\n        value: item.value,\n        itemStyle: {\n          color: colors[index % colors.length]\n        }\n      }))\n    },\n    // 配置构建 pieData 饼图数据 internalDiameterRatio:透明的空心占比\n    getPie3D (pieData, internalDiameterRatio) {\n      const that = this\n      const series = []\n      let sumValue = 0\n      let startValue = 0\n      let endValue = 0\n      let legendData = []\n      let legendBfb = []\n      const k = 1 - internalDiameterRatio\n      pieData.sort((a, b) => {\n        return (b.value - a.value)\n      })\n\n      // 标记前三名，用于显示线条指示\n      pieData.forEach((item, index) => {\n        item.isTopThree = index < 3\n        item.rank = index + 1\n      })\n\n      // 调试输出数据顺序\n      console.log('3D饼图数据顺序:', pieData.map(item => `${item.name}: ${item.value}`))\n      // 为每一个饼图数据，生成一个 series-surface(参数曲面) 配置\n      for (let i = 0; i < pieData.length; i++) {\n        sumValue += pieData[i].value\n        const seriesItem = {\n          // 系统名称\n          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,\n          type: 'surface',\n          // 是否为参数曲面（是）\n          parametric: true,\n          // 曲面图网格线（否）上面一根一根的\n          wireframe: {\n            show: false\n          },\n          pieData: pieData[i],\n          pieStatus: {\n            selected: false,\n            hovered: false,\n            k: k\n          },\n          // 设置饼图在容器中的位置(目前没发现啥用)\n          center: ['80%', '100%'],\n          radius: '60%'\n        }\n\n        // 曲面的颜色、不透明度等样式。\n        if (typeof pieData[i].itemStyle !== 'undefined') {\n          const itemStyle = {}\n          if (typeof pieData[i].itemStyle.color !== 'undefined') {\n            itemStyle.color = pieData[i].itemStyle.color\n          }\n          if (typeof pieData[i].itemStyle.opacity !== 'undefined') {\n            itemStyle.opacity = pieData[i].itemStyle.opacity\n          }\n          seriesItem.itemStyle = itemStyle\n        }\n        series.push(seriesItem)\n      }\n\n      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，\n      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。\n      legendData = []\n      legendBfb = []\n      for (let i = 0; i < series.length; i++) {\n        endValue = startValue + series[i].pieData.value\n        series[i].pieData.startRatio = startValue / sumValue\n        series[i].pieData.endRatio = endValue / sumValue\n        series[i].parametricEquation = that.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio,\n          false, false, k, series[i].pieData.value)\n        startValue = endValue\n        const bfb = that.fomatFloat(series[i].pieData.value / sumValue, 4)\n        legendData.push({\n          name: series[i].name,\n          value: bfb\n        })\n        legendBfb.push({\n          name: series[i].name,\n          value: bfb\n        })\n      }\n\n      // (第二个参数可以设置你这个环形的高低程度)\n      const boxHeight = this.getHeight3D(series, 20) // 通过传参设定3d饼/环的高度\n      // 准备待返回的配置项，把准备好的 legendData、series 传入。\n      const option = {\n        // 图例组件\n        legend: {\n          data: legendData,\n          // 图例列表的布局朝向 - 垂直布局\n          orient: 'vertical',\n          right: '5%', // 距离右边5%\n          top: 'center', // 垂直居中\n          itemWidth: 14,\n          itemHeight: 14,\n          itemGap: 30, // 增加间距\n          textStyle: {\n            color: '#A1E2FF',\n            fontSize: 13\n          },\n          // 简化格式化，只显示名称和百分比\n          formatter: function (param) {\n            const item = legendBfb.filter(item => item.name === param)[0]\n            const bfs = that.fomatFloat(item.value * 100, 1) + '%'\n            return `${item.name}: ${bfs}`\n          }\n        },\n        // 移动上去提示的文本内容\n        tooltip: {\n          formatter: params => {\n            if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n              const bfb = ((option.series[params.seriesIndex].pieData.endRatio - option.series[params.seriesIndex].pieData.startRatio) *\n                100).toFixed(2)\n              return `${params.seriesName}<br/>` +\n                `<span style=\"display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${params.color};\"></span>` +\n                `${bfb}%`\n            }\n          }\n        },\n        // 这个可以变形\n        xAxis3D: {\n          min: -1,\n          max: 1\n        },\n        yAxis3D: {\n          min: -1,\n          max: 1\n        },\n        zAxis3D: {\n          min: -1,\n          max: 1\n        },\n        // 此处是修改样式的重点\n        grid3D: {\n          show: false,\n          boxHeight: boxHeight, // 圆环的高度\n          // 这是饼图的位置 - 调整到合适位置\n          top: '-5%', // 适中的垂直位置\n          left: '-8%', // 适中的水平位置\n          width: '60%', // 限制图表宽度，为右侧图例留出空间\n          // 优化性能配置\n          postEffect: {\n            enable: false // 关闭后处理效果，提升性能\n          },\n          light: {\n            main: {\n              intensity: 1.2,\n              shadow: false // 关闭阴影，提升性能\n            },\n            ambient: {\n              intensity: 0.3\n            }\n          },\n          viewControl: { // 3d效果可以放大、旋转等，请自己去查看官方配置\n            alpha: 25, // 适中的俯视角度\n            beta: 0, // 水平旋转角度\n            distance: 150, // 适中的观察距离\n            center: [0, 0, 0], // 3D场景中心点\n            rotateSensitivity: 0, // 设置为0无法旋转\n            zoomSensitivity: 0, // 设置为0无法缩放\n            panSensitivity: 0, // 设置为0无法平移\n            autoRotate: false, // 自动旋转\n            animation: false // 关闭动画，提升性能\n          }\n        },\n        series: [\n          ...series\n          // 添加前三名的线条指示\n          // {\n          //   type: 'pie',\n          //   radius: ['30%', '60%'], // 调整半径以匹配3D饼图的视觉范围\n          //   center: ['30%', '45%'], // 微调中心点以更精确匹配3D饼图\n          //   startAngle: 30, // 进一步调整起始角度\n          //   clockwise: false, // 顺时针方向\n          //   // 使用完整数据以确保角度对应正确，但只为前三名设置标签\n          //   data: pieData.map(item => ({\n          //     name: item.name,\n          //     value: item.value,\n          //     itemStyle: {\n          //       color: 'transparent' // 透明，只显示标签线\n          //     },\n          //     label: {\n          //       show: item.isTopThree, // 只有前三名显示标签\n          //       position: 'outside',\n          //       fontSize: 12,\n          //       color: '#FFFFFF',\n          //       fontWeight: 'bold',\n          //       backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //       borderColor: '#A1E2FF',\n          //       borderWidth: 1,\n          //       borderRadius: 4,\n          //       padding: [3, 6],\n          //       formatter: `${item.name} ${item.value}人`\n          //     },\n          //     labelLine: {\n          //       show: item.isTopThree, // 只有前三名显示线条\n          //       length: 25, // 适中的第一段线条长度\n          //       length2: 15, // 适中的第二段线条长度\n          //       lineStyle: {\n          //         color: '#A1E2FF',\n          //         width: 2,\n          //         type: 'solid'\n          //       }\n          //     }\n          //   })),\n          //   // 全局标签配置（会被数据项中的配置覆盖）\n          //   label: {\n          //     show: false, // 默认不显示\n          //     position: 'outside',\n          //     fontSize: 12,\n          //     color: '#FFFFFF',\n          //     fontWeight: 'bold',\n          //     backgroundColor: 'rgba(0, 0, 0, 0.7)',\n          //     borderColor: '#A1E2FF',\n          //     borderWidth: 1,\n          //     borderRadius: 4,\n          //     padding: [3, 6],\n          //     formatter: function (params) {\n          //       return `${params.name} ${params.value}人`\n          //     }\n          //   },\n          //   labelLine: {\n          //     show: false, // 默认不显示\n          //     length: 25,\n          //     length2: 15,\n          //     lineStyle: {\n          //       color: '#A1E2FF',\n          //       width: 2,\n          //       type: 'solid'\n          //     }\n          //   },\n          //   silent: true,\n          //   z: 10\n          // }\n        ]\n      }\n      return option\n    },\n    // 获取3d饼图的最高扇区的高度\n    getHeight3D (series, height) {\n      series.sort((a, b) => {\n        return (b.pieData.value - a.pieData.value)\n      })\n      return height * 25 / series[0].pieData.value\n    },\n\n    // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation\n    getParametricEquation (startRatio, endRatio, isSelected, isHovered, k, h) {\n      // 计算\n      const midRatio = (startRatio + endRatio) / 2\n      const startRadian = startRatio * Math.PI * 2\n      const endRadian = endRatio * Math.PI * 2\n      const midRadian = midRatio * Math.PI * 2\n      // 如果只有一个扇形，则不实现选中效果。\n      if (startRatio === 0 && endRatio === 1) {\n        isSelected = false\n      }\n      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）\n      k = typeof k !== 'undefined' ? k : 1 / 3\n      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）\n      const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0\n      const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0\n      // 计算高亮效果的放大比例（未高亮，则比例为 1）\n      const hoverRate = isHovered ? 1.05 : 1\n      // 返回曲面参数方程\n      return {\n        u: {\n          min: -Math.PI,\n          max: Math.PI * 3,\n          step: Math.PI / 16\n        },\n        v: {\n          min: 0,\n          max: Math.PI * 2,\n          step: Math.PI / 10\n        },\n        x: function (u, v) {\n          if (u < startRadian) {\n            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        y: function (u, v) {\n          if (u < startRadian) {\n            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          if (u > endRadian) {\n            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate\n          }\n          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate\n        },\n        z: function (u, v) {\n          if (u < -Math.PI * 0.5) {\n            return Math.sin(u)\n          }\n          if (u > Math.PI * 2.5) {\n            return Math.sin(u) * h * 0.1\n          }\n          return Math.sin(v) > 0 ? 1 * h * 0.1 : -1\n        }\n      }\n    },\n\n    // 防抖函数\n    debounce (func, wait) {\n      let timeout\n      return function executedFunction (...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          func(...args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n    },\n\n    // 优化的更新图表方法\n    updateChartOption (option) {\n      if (this.isAnimating) return\n      this.isAnimating = true\n\n      // 使用requestAnimationFrame优化渲染\n      requestAnimationFrame(() => {\n        if (this.chart) {\n          this.chart.setOption(option, false, false) // 关闭动画和合并\n        }\n        this.isAnimating = false\n      })\n    },\n\n    // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。\n    bindListen (myChart) {\n      const that = this\n      let selectedIndex = ''\n      let hoveredIndex = ''\n      // 监听点击事件，实现选中效果（单选）\n      myChart.on('click', function (params) {\n        // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。\n        const isSelected = !that.option.series[params.seriesIndex].pieStatus.selected\n        const isHovered = that.option.series[params.seriesIndex].pieStatus.hovered\n        const k = that.option.series[params.seriesIndex].pieStatus.k\n        const startRatio = that.option.series[params.seriesIndex].pieData.startRatio\n        const endRatio = that.option.series[params.seriesIndex].pieData.endRatio\n        // 如果之前选中过其他扇形，将其取消选中（对 option 更新）\n        if (selectedIndex !== '' && selectedIndex !== params.seriesIndex) {\n          that.option.series[selectedIndex].parametricEquation = that.getParametricEquation(\n            that.option.series[selectedIndex].pieData.startRatio,\n            that.option.series[selectedIndex].pieData.endRatio,\n            false,\n            false,\n            k,\n            that.option.series[selectedIndex].pieData.value\n          )\n          that.option.series[selectedIndex].pieStatus.selected = false\n        }\n        // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）\n        that.option.series[params.seriesIndex].parametricEquation = that.getParametricEquation(startRatio, endRatio,\n          isSelected,\n          isHovered, k, that.option.series[params.seriesIndex].pieData.value)\n        that.option.series[params.seriesIndex].pieStatus.selected = isSelected\n        // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex\n        if (isSelected) {\n          selectedIndex = params.seriesIndex\n        }\n        // 使用优化的更新方法\n        that.updateChartOption(that.option)\n      })\n\n      // 防抖的mouseover处理\n      const debouncedMouseover = this.debounce((params) => {\n        that.handleMouseover(params, hoveredIndex, (newIndex) => {\n          hoveredIndex = newIndex\n        })\n      }, 16) // 约60fps\n\n      // 监听 mouseover，近似实现高亮（放大）效果\n      myChart.on('mouseover', debouncedMouseover)\n\n      // 修正取消高亮失败的 bug\n      myChart.on('globalout', function () {\n        if (hoveredIndex !== '') {\n          const isSelected = that.option.series[hoveredIndex].pieStatus.selected\n          const isHovered = false\n          const k = that.option.series[hoveredIndex].pieStatus.k\n          const startRatio = that.option.series[hoveredIndex].pieData.startRatio\n          const endRatio = that.option.series[hoveredIndex].pieData.endRatio\n\n          that.option.series[hoveredIndex].parametricEquation = that.getParametricEquation(\n            startRatio, endRatio, isSelected, isHovered, k,\n            that.option.series[hoveredIndex].pieData.value\n          )\n          that.option.series[hoveredIndex].pieStatus.hovered = isHovered\n          hoveredIndex = ''\n\n          // 使用优化的更新方法\n          that.updateChartOption(that.option)\n        }\n      })\n    },\n\n    // 提取mouseover处理逻辑\n    handleMouseover (params, hoveredIndex, setHoveredIndex) {\n      // 如果触发 mouseover 的扇形当前已高亮，则不做操作\n      if (hoveredIndex === params.seriesIndex) {\n        return // 已经高亮，不做操作\n      }\n\n      // 准备重新渲染扇形所需的参数\n      let isSelected, isHovered, startRatio, endRatio, k\n\n      // 如果当前有高亮的扇形，取消其高亮状态\n      if (hoveredIndex !== '') {\n        isSelected = this.option.series[hoveredIndex].pieStatus.selected\n        isHovered = false\n        startRatio = this.option.series[hoveredIndex].pieData.startRatio\n        endRatio = this.option.series[hoveredIndex].pieData.endRatio\n        k = this.option.series[hoveredIndex].pieStatus.k\n\n        this.option.series[hoveredIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[hoveredIndex].pieData.value\n        )\n        this.option.series[hoveredIndex].pieStatus.hovered = isHovered\n      }\n\n      // 如果触发 mouseover 的扇形不是透明圆环，将其高亮\n      if (params.seriesName !== 'mouseoutSeries' && params.seriesName !== 'pie2d') {\n        isSelected = this.option.series[params.seriesIndex].pieStatus.selected\n        isHovered = true\n        startRatio = this.option.series[params.seriesIndex].pieData.startRatio\n        endRatio = this.option.series[params.seriesIndex].pieData.endRatio\n        k = this.option.series[params.seriesIndex].pieStatus.k\n\n        this.option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(\n          startRatio, endRatio, isSelected, isHovered, k,\n          this.option.series[params.seriesIndex].pieData.value + 5\n        )\n        this.option.series[params.seriesIndex].pieStatus.hovered = isHovered\n        setHoveredIndex(params.seriesIndex)\n      }\n\n      // 使用优化的更新方法\n      this.updateChartOption(this.option)\n    },\n\n    // 这是一个自定义计算的方法\n    fomatFloat (num, n) {\n      var f = parseFloat(num)\n      if (isNaN(f)) {\n        return false\n      }\n      f = Math.round(num * Math.pow(10, n)) / Math.pow(10, n) // n 幂\n      var s = f.toString()\n      var rs = s.indexOf('.')\n      // 判定如果是整数，增加小数点再补0\n      if (rs < 0) {\n        rs = s.length\n        s += '.'\n      }\n      while (s.length <= rs + n) {\n        s += '0'\n      }\n      return s\n    },\n\n    resizeChart () {\n      if (this.chart) {\n        this.chart.resize()\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.party-distribution-chart {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图(外面的容器)\n.container {\n  width: 100%;\n  height: 100%;\n}\n\n// 饼图的大小\n.chartsGl {\n  height: 100%;\n  width: 100%;\n}\n</style>\n"]}]}